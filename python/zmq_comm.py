#!/usr/bin/env python3
"""
ZMQ Communication Module for Robot Control
Based on CommBase.h interface design

This module provides ZMQ-based communication for robot control systems,
implementing the same interface as the C++ CommBase class.
"""

import zmq
import struct
import time
import threading
import json
from typing import Callable, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum


class CommType(Enum):
    """Communication type enumeration"""
    ZMQ = "zmq"
    FIFO = "fifo"


@dataclass
class RobotState:
    """Robot state structure matching C++ RobotState"""
    timestamp_us: int = 0
    position: list = None  # 6 elements
    velocity: list = None  # 6 elements
    current: list = None   # 6 elements
    servo_status: int = 0
    
    def __post_init__(self):
        if self.position is None:
            self.position = [0.0] * 6
        if self.velocity is None:
            self.velocity = [0.0] * 6
        if self.current is None:
            self.current = [0.0] * 6
    
    def to_bytes(self) -> bytes:
        """Convert to binary format for transmission"""
        # Pack: timestamp_us(Q) + position(6d) + velocity(6d) + current(6d) + servo_status(Q)
        return struct.pack('Q6d6d6dQ', 
                          self.timestamp_us,
                          *self.position,
                          *self.velocity, 
                          *self.current,
                          self.servo_status)
    
    @classmethod
    def from_bytes(cls, data: bytes) -> 'RobotState':
        """Create from binary data"""
        unpacked = struct.unpack('Q6d6d6dQ', data)
        return cls(
            timestamp_us=unpacked[0],
            position=list(unpacked[1:7]),
            velocity=list(unpacked[7:13]),
            current=list(unpacked[13:19]),
            servo_status=unpacked[19]
        )


@dataclass
class ServoCommand:
    """Servo command structure matching C++ ServoCommand"""
    timestamp_us: int = 0
    position: list = None  # 6 elements
    velocity: list = None  # 6 elements
    duration_ms: int = 0
    
    def __post_init__(self):
        if self.position is None:
            self.position = [0.0] * 6
        if self.velocity is None:
            self.velocity = [0.0] * 6
    
    def to_bytes(self) -> bytes:
        """Convert to binary format for transmission"""
        # Pack: timestamp_us(Q) + position(6d) + velocity(6d) + duration_ms(Q)
        return struct.pack('Q6d6dQ',
                          self.timestamp_us,
                          *self.position,
                          *self.velocity,
                          self.duration_ms)
    
    @classmethod
    def from_bytes(cls, data: bytes) -> 'ServoCommand':
        """Create from binary data"""
        unpacked = struct.unpack('Q6d6dQ', data)
        return cls(
            timestamp_us=unpacked[0],
            position=list(unpacked[1:7]),
            velocity=list(unpacked[7:13]),
            duration_ms=unpacked[13]
        )


class ZMQComm:
    """
    ZMQ Communication class implementing CommBase interface
    
    This class provides ZMQ-based communication for robot control systems,
    supporting both server (hardware) and client (robot communication layer) modes.
    """
    
    # IPC endpoints - matching C++ implementation
    CMD_ENDPOINT = "ipc:///tmp/cmd"
    SERVO_DATA_ENDPOINT = "ipc:///tmp/servo_data"
    FEEDBACK_DATA_ENDPOINT = "ipc:///tmp/feedback_data"
    ERROR_ENDPOINT = "ipc:///tmp/error"
    
    def __init__(self, is_server: bool = False):
        """
        Initialize ZMQ communication
        
        Args:
            is_server: True for hardware side, False for robot communication side
        """
        self.is_server = is_server
        self.context = zmq.Context()
        
        # Initialize sockets
        self.req_sock = None      # Request socket (client side)
        self.rep_sock = None      # Reply socket (server side)
        self.servo_push = None    # Servo command push socket (client side)
        self.servo_pull = None    # Servo command pull socket (server side)
        self.feedback_pub = None  # Feedback publisher
        self.feedback_sub = None  # Feedback subscriber
        self.error_pub = None     # Error publisher
        self.error_sub = None     # Error subscriber
        
        # Connection state
        self._connected = False
        self._running = False
        
        # Callback storage
        self._servo_callback = None
        self._robot_state_callback = None
        self._feedback_callback = None
        self._error_callback = None
        
        # Background threads
        self._threads = []
        
        print(f"[ZMQ] Constructed, is_server={self.is_server}")
    
    def __del__(self):
        """Destructor"""
        self.close()
    
    def init(self) -> bool:
        """
        Initialize ZMQ sockets and connections
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            if self.is_server:
                # Server (hardware) side
                self.rep_sock = self.context.socket(zmq.REP)
                self.rep_sock.bind(self.CMD_ENDPOINT)
                print(f"[ZMQ] REP bind to {self.CMD_ENDPOINT}")
                
                # Pull servo commands (Server pulls from Client)
                self.servo_pull = self.context.socket(zmq.PULL)
                self.servo_pull.bind(self.SERVO_DATA_ENDPOINT)
                print(f"[ZMQ] PULL bind to {self.SERVO_DATA_ENDPOINT}")
                
                # Publish feedback data
                self.feedback_pub = self.context.socket(zmq.PUB)
                self.feedback_pub.bind(self.FEEDBACK_DATA_ENDPOINT)
                print(f"[ZMQ] PUB bind to {self.FEEDBACK_DATA_ENDPOINT}")
                
                # Publish error data
                self.error_pub = self.context.socket(zmq.PUB)
                self.error_pub.bind(self.ERROR_ENDPOINT)
                print(f"[ZMQ] Error PUB bind to {self.ERROR_ENDPOINT}")
                
            else:
                # Client (robot communication) side
                self.req_sock = self.context.socket(zmq.REQ)
                self.req_sock.connect(self.CMD_ENDPOINT)
                print(f"[ZMQ] REQ connect to {self.CMD_ENDPOINT}")
                
                # Push servo commands (Client pushes to Server)
                self.servo_push = self.context.socket(zmq.PUSH)
                self.servo_push.connect(self.SERVO_DATA_ENDPOINT)
                print(f"[ZMQ] PUSH connect to {self.SERVO_DATA_ENDPOINT}")
                
                # Subscribe to feedback data
                self.feedback_sub = self.context.socket(zmq.SUB)
                self.feedback_sub.setsockopt(zmq.SUBSCRIBE, b"")
                self.feedback_sub.connect(self.FEEDBACK_DATA_ENDPOINT)
                print(f"[ZMQ] Feedback SUB connect to {self.FEEDBACK_DATA_ENDPOINT}")
                
                # Subscribe to error data
                self.error_sub = self.context.socket(zmq.SUB)
                self.error_sub.setsockopt(zmq.SUBSCRIBE, b"")
                self.error_sub.connect(self.ERROR_ENDPOINT)
                print(f"[ZMQ] Error SUB connect to {self.ERROR_ENDPOINT}")
            
            # Small delay for socket setup
            time.sleep(0.1)
            
            self._connected = True
            self._running = True
            print("[ZMQ] Initialization successful")
            return True
            
        except Exception as e:
            print(f"[ZMQ] Initialization failed: {e}")
            return False
    
    def close(self):
        """Close all sockets and cleanup"""
        try:
            self._running = False
            
            # Wait for threads to finish
            for thread in self._threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)
            
            # Close sockets
            sockets = [self.req_sock, self.rep_sock, self.servo_push, self.servo_pull,
                      self.feedback_pub, self.feedback_sub, self.error_pub, self.error_sub]
            
            for sock in sockets:
                if sock is not None:
                    sock.close()
            
            self.context.term()
            self._connected = False
            print("[ZMQ] All sockets and context closed")
            
        except Exception as e:
            print(f"[ZMQ] Exception in close: {e}")
    
    def is_connected(self) -> bool:
        """Check if connection is established"""
        return self._connected

    def request_reply(self, cmd: str) -> str:
        """
        Send request and wait for reply (Request-Reply pattern)

        Args:
            cmd: Command string to send

        Returns:
            Reply string from server
        """
        if not self.is_connected() or self.req_sock is None:
            return ""

        try:
            # Send request
            self.req_sock.send_string(cmd)

            # Wait for reply with timeout
            if self.req_sock.poll(5000):  # 5 second timeout
                reply = self.req_sock.recv_string()
                return reply
            else:
                print("[ZMQ] Request timeout")
                return ""

        except Exception as e:
            print(f"[ZMQ] Request-reply error: {e}")
            return ""

    def send_rt_data_servo(self, cmd: ServoCommand) -> bool:
        """
        Send real-time servo command data (using PUSH socket)

        Args:
            cmd: ServoCommand to send

        Returns:
            True if sent successfully, False otherwise
        """
        if not self.is_connected() or self.servo_push is None:
            return False

        try:
            # 添加字符头 "SERVO_CMD:"
            header = b"SERVO_CMD:"
            data = cmd.to_bytes()
            message = header + data
            self.servo_push.send(message, zmq.NOBLOCK)
            return True
        except Exception as e:
            print(f"[ZMQ] Send servo data error: {e}")
            return False

    def send_rt_data_robot(self, state: RobotState) -> bool:
        """
        Send real-time robot state data

        Args:
            state: RobotState to send

        Returns:
            True if sent successfully, False otherwise
        """
        if not self.is_connected() or self.feedback_pub is None:
            return False

        try:
            data = state.to_bytes()
            self.feedback_pub.send(data, zmq.NOBLOCK)
            return True
        except Exception as e:
            print(f"[ZMQ] Send robot state error: {e}")
            return False

    def recv_rt_data_servo(self, callback: Callable[[ServoCommand], None]) -> bool:
        """
        Receive real-time servo command data with callback (using PULL socket)

        Args:
            callback: Function to call when servo command received

        Returns:
            True if receiver started successfully, False otherwise
        """
        if not self.is_connected() or self.servo_pull is None:
            return False

        self._servo_callback = callback

        def receiver_thread():
            last_timestamp_us = 0
            recv_count = 0
            start_time = time.time()

            while self._running:
                try:
                    if self.servo_pull.poll(100):  # 100ms timeout
                        message = self.servo_pull.recv(zmq.NOBLOCK)
                        recv_count += 1
                        current_time = time.time()

                        # 检查并解析字符头 "SERVO_CMD:"
                        header = b"SERVO_CMD:"
                        if message.startswith(header):
                            # 提取实际数据部分
                            data = message[len(header):]
                            cmd = ServoCommand.from_bytes(data)

                            # 计算接收间隔
                            interval_ms = 0.0
                            if last_timestamp_us > 0:
                                interval_ms = (cmd.timestamp_us - last_timestamp_us) / 1000.0

                            # 计算接收频率
                            elapsed_s = current_time - start_time
                            avg_freq = recv_count / elapsed_s if elapsed_s > 0 else 0.0

                            print(f"[ZMQ] Received servo command [{recv_count}]: "
                                  f"ts={cmd.timestamp_us} duration={cmd.duration_ms}ms "
                                  f"interval={interval_ms:.2f}ms freq={avg_freq:.1f}Hz")

                            last_timestamp_us = cmd.timestamp_us

                            if self._servo_callback:
                                self._servo_callback(cmd)
                        else:
                            print(f"[ZMQ] Invalid servo message header, expected 'SERVO_CMD:', got: {message[:20]}")

                except Exception as e:
                    if self._running:
                        print(f"[ZMQ] Servo receiver error: {e}")
                    break

        thread = threading.Thread(target=receiver_thread, daemon=True)
        thread.start()
        self._threads.append(thread)
        return True

    def recv_rt_data_robot(self, callback: Callable[[RobotState], None]) -> bool:
        """
        Receive real-time robot state data with callback

        Args:
            callback: Function to call when robot state received

        Returns:
            True if receiver started successfully, False otherwise
        """
        if not self.is_connected() or self.feedback_sub is None:
            return False

        self._robot_state_callback = callback

        def receiver_thread():
            while self._running:
                try:
                    if self.feedback_sub.poll(100):  # 100ms timeout
                        data = self.feedback_sub.recv(zmq.NOBLOCK)
                        state = RobotState.from_bytes(data)
                        if self._robot_state_callback:
                            self._robot_state_callback(state)
                except Exception as e:
                    if self._running:
                        print(f"[ZMQ] Robot state receiver error: {e}")
                    break

        thread = threading.Thread(target=receiver_thread, daemon=True)
        thread.start()
        self._threads.append(thread)
        return True

    def recv_reply(self) -> str:
        """
        Receive command and return reply (for server side)

        Returns:
            Command string received from client
        """
        if not self.is_connected() or self.rep_sock is None:
            return ""

        try:
            if self.rep_sock.poll(100):  # 100ms timeout
                cmd = self.rep_sock.recv_string(zmq.NOBLOCK)
                return cmd
            return ""
        except Exception as e:
            print(f"[ZMQ] Receive reply error: {e}")
            return ""

    def send_reply(self, reply: str) -> bool:
        """
        Send reply to client (for server side)

        Args:
            reply: Reply string to send

        Returns:
            True if sent successfully, False otherwise
        """
        if not self.is_connected() or self.rep_sock is None:
            return False

        try:
            self.rep_sock.send_string(reply)
            return True
        except Exception as e:
            print(f"[ZMQ] Send reply error: {e}")
            return False

    def send_feedback_data(self, data: str) -> bool:
        """
        Send feedback data

        Args:
            data: Feedback data string to send

        Returns:
            True if sent successfully, False otherwise
        """
        if not self.is_connected() or self.feedback_pub is None:
            return False

        try:
            self.feedback_pub.send_string(data, zmq.NOBLOCK)
            return True
        except Exception as e:
            print(f"[ZMQ] Send feedback data error: {e}")
            return False

    def recv_feedback_data(self, callback: Callable[[str], None]) -> bool:
        """
        Receive feedback data with callback

        Args:
            callback: Function to call when feedback data received

        Returns:
            True if receiver started successfully, False otherwise
        """
        if not self.is_connected() or self.feedback_sub is None:
            return False

        self._feedback_callback = callback

        def receiver_thread():
            while self._running:
                try:
                    if self.feedback_sub.poll(100):  # 100ms timeout
                        data = self.feedback_sub.recv_string(zmq.NOBLOCK)
                        if self._feedback_callback:
                            self._feedback_callback(data)
                except Exception as e:
                    if self._running:
                        print(f"[ZMQ] Feedback receiver error: {e}")
                    break

        thread = threading.Thread(target=receiver_thread, daemon=True)
        thread.start()
        self._threads.append(thread)
        return True

    def send_error(self, error: str) -> bool:
        """
        Send error message

        Args:
            error: Error message string to send

        Returns:
            True if sent successfully, False otherwise
        """
        if not self.is_connected() or self.error_pub is None:
            return False

        try:
            self.error_pub.send_string(error, zmq.NOBLOCK)
            return True
        except Exception as e:
            print(f"[ZMQ] Send error error: {e}")
            return False

    def recv_error(self, callback: Callable[[str], None]) -> bool:
        """
        Receive error messages with callback

        Args:
            callback: Function to call when error message received

        Returns:
            True if receiver started successfully, False otherwise
        """
        if not self.is_connected() or self.error_sub is None:
            return False

        self._error_callback = callback

        def receiver_thread():
            while self._running:
                try:
                    if self.error_sub.poll(100):  # 100ms timeout
                        error = self.error_sub.recv_string(zmq.NOBLOCK)
                        if self._error_callback:
                            self._error_callback(error)
                except Exception as e:
                    if self._running:
                        print(f"[ZMQ] Error receiver error: {e}")
                    break

        thread = threading.Thread(target=receiver_thread, daemon=True)
        thread.start()
        self._threads.append(thread)
        return True

    def get_current_timestamp(self) -> int:
        """
        Get current timestamp in microseconds

        Returns:
            Current timestamp in microseconds
        """
        return int(time.time() * 1_000_000)

    def get_connection_info(self) -> str:
        """
        Get connection information

        Returns:
            Connection information string
        """
        info = {
            "type": "ZMQ",
            "is_server": self.is_server,
            "connected": self._connected,
            "endpoints": {
                "cmd": self.CMD_ENDPOINT,
                "servo_data": self.SERVO_DATA_ENDPOINT,
                "feedback_data": self.FEEDBACK_DATA_ENDPOINT,
                "error": self.ERROR_ENDPOINT
            }
        }
        return json.dumps(info, indent=2)

    @staticmethod
    def build_servo_command(mode: int, gain: float, smooth: float, period: int) -> str:
        """
        Build START_SERVO command string (C++ style)

        Args:
            mode: Servo mode
            gain: Control gain
            smooth: Smoothing factor
            period: Control period

        Returns:
            Formatted command string
        """
        return f"START_SERVO {mode} {gain} {smooth} {period}"

    @staticmethod
    def build_position_command(positions: list) -> str:
        """
        Build SET_POSITION command string (C++ style)

        Args:
            positions: List of 6 position values

        Returns:
            Formatted command string
        """
        if len(positions) != 6:
            raise ValueError("Position list must contain exactly 6 values")

        pos_str = " ".join(f"{pos:.6f}" for pos in positions)
        return f"SET_POSITION {pos_str}"

    @staticmethod
    def parse_position_reply(reply: str) -> list:
        """
        Parse POSITION reply string (C++ style)

        Args:
            reply: Reply string like "POSITION 1.0 2.0 3.0 4.0 5.0 6.0"

        Returns:
            List of 6 position values, or empty list if parsing fails
        """
        try:
            parts = reply.split()
            if len(parts) == 7 and parts[0] == "POSITION":
                return [float(x) for x in parts[1:7]]
            return []
        except (ValueError, IndexError):
            return []

    @staticmethod
    def parse_servo_reply(reply: str) -> dict:
        """
        Parse START_SERVO reply string (C++ style)

        Args:
            reply: Reply string like "SERVO_STARTED mode=1 gain=0.8 smooth=0.1 period=10"

        Returns:
            Dictionary with parsed parameters, or empty dict if parsing fails
        """
        try:
            if not reply.startswith("SERVO_STARTED"):
                return {}

            # Extract parameters
            params = {}
            parts = reply.split()
            for part in parts[1:]:  # Skip "SERVO_STARTED"
                if "=" in part:
                    key, value = part.split("=", 1)
                    if key in ["mode", "period"]:
                        params[key] = int(value)
                    elif key in ["gain", "smooth"]:
                        params[key] = float(value)

            return params
        except (ValueError, IndexError):
            return {}


# ============================================================================
# Example Usage and Test Functions
# ============================================================================

def test_server():
    """Test server (hardware) side"""
    print("=== Starting Server (Hardware) Side ===")

    # Create server instance
    server = ZMQComm(is_server=True)

    if not server.init():
        print("Failed to initialize server")
        return

    print("Server initialized successfully")

    # Set up servo command receiver
    def on_servo_command(cmd: ServoCommand):
        print(f"[Server] Received servo command:")
        print(f"  Timestamp: {cmd.timestamp_us}")
        print(f"  Position: {cmd.position}")
        print(f"  Velocity: {cmd.velocity}")
        print(f"  Duration: {cmd.duration_ms}ms")

    server.recv_rt_data_servo(on_servo_command)

    # Set up feedback data receiver
    def on_feedback(data: str):
        print(f"[Server] Received feedback: {data}")

    server.recv_feedback_data(on_feedback)

    # Set up error receiver
    def on_error(error: str):
        print(f"[Server] Received error: {error}")

    server.recv_error(on_error)

    # Handle request-reply
    def handle_requests():
        while server.is_connected():
            cmd = server.recv_reply()
            if cmd:
                print(f"[Server] Received command: {cmd}")

                # Process command and send reply (C++ style commands)
                if cmd == "get_status":
                    reply = "STATUS_OK"
                elif cmd == "emergency_stop":
                    reply = "EMERGENCY_STOPPED"
                elif cmd.startswith("START_SERVO"):
                    # Parse START_SERVO mode gain smooth period
                    parts = cmd.split()
                    if len(parts) == 5:
                        mode, gain, smooth, period = parts[1:5]
                        reply = f"SERVO_STARTED mode={mode} gain={gain} smooth={smooth} period={period}"
                    else:
                        reply = "INVALID_SERVO_PARAMS"
                elif cmd == "STOP_SERVO":
                    reply = "SERVO_STOPPED"
                elif cmd == "GET_POSITION":
                    reply = "POSITION 1.234 0.567 0.890 0.123 0.456 0.789"
                elif cmd.startswith("SET_POSITION"):
                    # Parse SET_POSITION x y z rx ry rz
                    parts = cmd.split()
                    if len(parts) == 7:
                        positions = " ".join(parts[1:7])
                        reply = f"POSITION_SET {positions}"
                    else:
                        reply = "INVALID_POSITION_PARAMS"
                elif cmd.startswith("move_to"):
                    reply = "MOVING"
                else:
                    reply = "UNKNOWN_COMMAND"

                server.send_reply(reply)
                print(f"[Server] Sent reply: {reply}")

            time.sleep(0.01)  # 10ms loop

    # Start request handler thread
    request_thread = threading.Thread(target=handle_requests, daemon=True)
    request_thread.start()

    # Simulate sending robot state data
    def send_robot_states():
        counter = 0
        while server.is_connected():
            # Create robot state
            state = RobotState(
                timestamp_us=server.get_current_timestamp(),
                position=[counter * 0.1, 0.0, 0.0, 0.0, 0.0, 0.0],
                velocity=[0.1, 0.0, 0.0, 0.0, 0.0, 0.0],
                current=[1.0, 1.0, 1.0, 1.0, 1.0, 1.0],
                servo_status=1
            )

            server.send_rt_data_robot(state)
            counter += 1

            # Send some feedback data
            if counter % 100 == 0:
                server.send_feedback_data(f"Status update #{counter}")

            # Send error occasionally
            if counter % 500 == 0:
                server.send_error(f"Warning: High temperature detected at step {counter}")

            time.sleep(0.01)  # 100Hz

    # Start robot state sender thread
    state_thread = threading.Thread(target=send_robot_states, daemon=True)
    state_thread.start()

    print("Server running... Press Ctrl+C to stop")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down server...")
        server.close()


def test_client():
    """Test client (robot communication) side"""
    print("=== Starting Client (Robot Communication) Side ===")

    # Create client instance
    client = ZMQComm(is_server=False)

    if not client.init():
        print("Failed to initialize client")
        return

    print("Client initialized successfully")
    print("Connection info:")
    print(client.get_connection_info())

    # Set up robot state receiver
    def on_robot_state(state: RobotState):
        pass
        # print(f"[Client] Received robot state:")
        # print(f"  Timestamp: {state.timestamp_us}")
        # print(f"  Position: {state.position}")
        # print(f"  Velocity: {state.velocity}")
        # print(f"  Current: {state.current}")
        # print(f"  Status: {state.servo_status}")

    client.recv_rt_data_robot(on_robot_state)

    # Set up feedback receiver
    def on_feedback(data: str):
        print(f"[Client] Received feedback: {data}")

    client.recv_feedback_data(on_feedback)

    # Set up error receiver
    def on_error(error: str):
        print(f"[Client] Received error: {error}")

    client.recv_error(on_error)

    # Test request-reply
    print("\n=== Testing Request-Reply ===")

    # 构造命令字符串：START_SERVO mode gain smooth period
    mode = 1
    gain = 10
    smooth = 20
    period = 16
    start_servo_cmd = f"START_SERVO {mode} {gain} {smooth} {period}"

    commands = [
        start_servo_cmd
    ]

    for cmd in commands:
        print(f"[Client] Sending command: {cmd}")
        reply = client.request_reply(cmd)
        print(f"[Client] Received reply: {reply}")
        time.sleep(0.5)

    # Send servo commands
    print("\n=== Sending Servo Commands ===")
    for i in range(10):
        servo_cmd = ServoCommand(
            timestamp_us=client.get_current_timestamp(),
            position=[i * 0.1, 0.0, 0.0, 0.0, 0.0, 0.0],
            velocity=[0.1, 0.0, 0.0, 0.0, 0.0, 0.0],
            duration_ms=100
        )
        print(f"[Client] Sent  pos:{servo_cmd.position[0]}")
        client.send_rt_data_servo(servo_cmd)

        time.sleep(0.1)

    print("\nClient running... Press Ctrl+C to stop")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down client...")
        client.close()


def main():
    """Main function for running examples"""
    import sys

    if len(sys.argv) != 2:
        print("Usage: python zmq_comm.py [server|client]")
        print("  server - Run as hardware server")
        print("  client - Run as robot communication client")
        sys.exit(1)

    mode = sys.argv[1].lower()

    if mode == "server":
        test_server()
    elif mode == "client":
        test_client()
    else:
        print("Invalid mode. Use 'server' or 'client'")
        sys.exit(1)


if __name__ == "__main__":
    main()
