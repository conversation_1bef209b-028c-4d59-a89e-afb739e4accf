[tox]
envlist = py36, py37, py38, py39, py310, lint
isolated_build = True

# This is required to activate PEP-517 during testing with tox.
# https://tox.readthedocs.io/en/latest/example/package.html#setuptools


[gh-actions]
python =
    3.7: py37
    3.7: py37
    3.8: py38, lint
    3.9: py39
    3.10: py310

[testenv]
sitepackages=false
allowlist_externals =
    sh
    invoke

deps =
    -r requirements3.txt

# These commands are ran before the actual testing
commands_pre =  
    invoke install-solvers  # install qpoases and other solver here
    - sh -c 'find tests -name "*.pyc" -delete'  # remove tmp files

commands =
    pytest -x tests --junitxml=test-results/junit.xml

[testenv:lint]
basepython=python3.9
commands = 
    invoke type-check
    invoke lint

[pycodestyle]
ignore = E731,W503,W605
max-line-length = 120
statistics = True