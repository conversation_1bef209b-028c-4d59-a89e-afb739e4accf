"""A script that generate test data
"""
from scipy.interpolate import CubicSpline
import numpy as np

np.random.seed(0)


def write_1d(ctn, x):
    ctn += f"1 {len(x)}\n"
    for x_i in x:
        ctn += str(x_i) + " "
    ctn += "\n"
    return ctn


def write_2d(ctn, y):
    r, c = y.shape
    ctn += f"2 {r} {c}\n"
    for y_i in y:
        for y_ii in y_i:
            ctn += str(y_ii) + " "
        ctn += "\n"
    ctn += "\n"
    return ctn


ctn_full = f"""
// This file is automatically generated. Do not change.
// To regenerate this file run: python {__file__}
"""

# Test 1
test_name = "test1"
x = np.linspace(0, 1, 10)
y = np.random.randn(10, 3)
bc_type = "clamped"
spl = CubicSpline(x, y, bc_type=bc_type)
x_test = np.linspace(0, 1, 4)
y_test = spl(x_test)
yd_test = spl(x_test, 1)
ydd_test = spl(x_test, 2)
ctn = ""
ctn = write_1d(ctn, x)
ctn = write_2d(ctn, y)
ctn += f"{bc_type} {bc_type} \n"
ctn = write_1d(ctn, x_test)
ctn = write_2d(ctn, y_test)
ctn = write_2d(ctn, yd_test)
ctn = write_2d(ctn, ydd_test)
ctn_full += f"""
const char * _toppra_cubic_spline_{test_name} = R\"TEST(
{ctn}
)TEST\";
"""

# Test 2
test_name = "test2"
x = np.linspace(0, 2.3, 8)
y = np.random.randn(8, 3)
bc_type = "natural"
spl = CubicSpline(x, y, bc_type=bc_type)
x_test = np.linspace(0, 2.3, 45)
y_test = spl(x_test)
yd_test = spl(x_test, 1)
ydd_test = spl(x_test, 2)
ctn = ""
ctn = write_1d(ctn, x)
ctn = write_2d(ctn, y)
ctn += f"{bc_type} {bc_type} \n"
ctn = write_1d(ctn, x_test)
ctn = write_2d(ctn, y_test)
ctn = write_2d(ctn, yd_test)
ctn = write_2d(ctn, ydd_test)
ctn_full += f"""
const char * _toppra_cubic_spline_{test_name} = R\"TEST(
{ctn}
)TEST\";
"""

# Test 3: mixed
test_name = "test3"
x = np.linspace(0, 2.3, 8)
y = np.random.randn(8, 3)
bc_type = ("natural", "clamped")
spl = CubicSpline(x, y, bc_type=bc_type)
x_test = np.linspace(0, 2.3, 45)
y_test = spl(x_test)
yd_test = spl(x_test, 1)
ydd_test = spl(x_test, 2)
ctn = ""
ctn = write_1d(ctn, x)
ctn = write_2d(ctn, y)
ctn += f"{bc_type[0]} {bc_type[1]} \n"
ctn = write_1d(ctn, x_test)
ctn = write_2d(ctn, y_test)
ctn = write_2d(ctn, yd_test)
ctn = write_2d(ctn, ydd_test)
ctn_full += f"""
const char * _toppra_cubic_spline_{test_name} = R\"TEST(
{ctn}
)TEST\";
"""

# Combine all tests
with open("test_cubic_spline_gendata_out.hpp", "w") as f:
    f.write(ctn_full)
