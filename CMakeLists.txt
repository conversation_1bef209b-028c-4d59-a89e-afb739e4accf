cmake_minimum_required(VERSION 3.16)
project(robot_infra VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find packages
find_package(Eigen3 REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(ZMQ REQUIRED libzmq)

# Try to find Pinocchio (required for robot model)
find_package(pinocchio QUIET)

# Try to find <PERSON><PERSON><PERSON> and <PERSON><PERSON> (optional)
find_package(ruckig QUIET)
find_package(toppra QUIET)

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/include/comm
    ${CMAKE_SOURCE_DIR}/include/trajectory
)

# 收集库实现文件（排除 main 程序）
file(GLOB COMM_SRCS src/comm/*.cpp)
file(GLOB TRAJ_SRCS src/trajectory/*.cpp)
set(LIB_SRCS
    ${COMM_SRCS}
    ${TRAJ_SRCS}
    src/industRob.cpp
    src/RobotModel.cpp
)

# 过滤掉不需要的文件
list(REMOVE_ITEM LIB_SRCS ${CMAKE_SOURCE_DIR}/src/trajectory/online_tracker.cpp)
list(REMOVE_ITEM LIB_SRCS ${CMAKE_SOURCE_DIR}/src/comm/industRob.cpp)

# 创建动态库
add_library(robot_infra SHARED ${LIB_SRCS})



# 设置库版本信息
set_target_properties(robot_infra PROPERTIES
    VERSION 1.0.0
    SOVERSION 1
    OUTPUT_NAME robot_infra
)

# 链接库
target_link_libraries(robot_infra
    Eigen3::Eigen
    ${ZMQ_LIBRARIES}
)

# Add Pinocchio if found
if(pinocchio_FOUND)
    target_link_libraries(robot_infra pinocchio::pinocchio)
    target_compile_definitions(robot_infra PRIVATE PINOCCHIO_FOUND)

    message(STATUS "Found Pinocchio: ${pinocchio_VERSION}")
else()
    message(WARNING "Pinocchio not found. Robot model functionality will be limited.")
endif()

# Add optional libraries if found
if(ruckig_FOUND)
    target_link_libraries(robot_infra ruckig::ruckig)
    target_compile_definitions(robot_infra PRIVATE RUCKIG_FOUND)
endif()

if(toppra_FOUND)
    target_link_libraries(robot_infra toppra::toppra)
    target_compile_definitions(robot_infra PRIVATE TOPPRA_FOUND)
endif()

# 设置包含目录
target_include_directories(robot_infra PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include/robot_infra>
    ${ZMQ_INCLUDE_DIRS}
)

# Debug宏开关
option(ENABLE_DEBUG "Enable debug mode (use test_ FIFO/ZMQ endpoints)" OFF)
if(ENABLE_DEBUG)
    add_definitions(-DDEBUG)
    message(STATUS "ENABLE_DEBUG is ON")
endif()

# 安装规则
include(GNUInstallDirs)

# 安装动态库
install(TARGETS robot_infra
    EXPORT robot_infraTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

# 安装头文件
install(DIRECTORY include/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/robot_infra
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)

# 安装导出目标
install(EXPORT robot_infraTargets
    FILE robot_infraTargets.cmake
    NAMESPACE robot_infra::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/robot_infra
)

# 打包配置
set(CPACK_PACKAGE_NAME "robot_infra")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Industrial Robot Infrastructure Library")
set(CPACK_PACKAGE_VENDOR "Industrial Robot Team")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# 设置包类型
set(CPACK_GENERATOR "TGZ;DEB")
set(CPACK_SOURCE_GENERATOR "TGZ")

# DEB包配置
set(CPACK_DEBIAN_PACKAGE_DEPENDS "libeigen3-dev, libzmq3-dev")
set(CPACK_DEBIAN_PACKAGE_SECTION "libs")
set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")

include(CPack)

# 添加子目录
add_subdirectory(test)
add_subdirectory(docs)
add_subdirectory(examples)

# 显示信息
message(STATUS "Building robot_infra as SHARED library")
message(STATUS "Library sources: ${LIB_SRCS}")
message(STATUS "Optional dependencies:")
message(STATUS "  Ruckig: ${ruckig_FOUND}")
message(STATUS "  Toppra: ${toppra_FOUND}")
