# Robot Infrastructure

A comprehensive C++ robot control and communication system with trajectory planning capabilities.

## Features

- **Real-time Communication**: ZeroMQ-based communication system for robot control
- **Trajectory Planning**: Integration with Ruckig and TOPPRA libraries
- **Kinematics**: Forward and inverse kinematics calculations
- **Industrial Robot Interface**: Complete API for robot control and monitoring
- **Multi-threaded Architecture**: Separate threads for communication and control

## Project Structure

```
robot_infra/
├── CMakeLists.txt          # Root build configuration
├── README.md               # This file
├── include/                # Header files
│   ├── comm/              # Communication interfaces
│   ├── trajectory/        # Trajectory planning
│   └── industRob.h        # Main robot interface
├── src/                   # Source files
│   ├── comm/              # Communication implementation
│   ├── trajectory/        # Trajectory planning implementation
│   └── industRob.cpp      # Main robot implementation
├── test/                  # Test programs and examples
│   ├── comm_test.cpp      # Communication tests
│   ├── hw_sim_test.cpp    # Hardware simulation tests
│   ├── toppra_fifo_demo.cpp # TOPPRA demonstration
│   └── toppra/            # TOPPRA examples
├── doc/                   # Documentation
├── cmake/                 # CMake modules
└── 3rdparty/              # Third-party libraries
    ├── ruckig/            # Ruckig trajectory generation
    └── toppra/            # TOPPRA path parameterization
```

## Dependencies

### Required
- **Ubuntu 18.04+** or compatible Linux distribution
- **C++17** compiler (GCC 7+ or Clang 6+)
- **CMake 3.16+**
- **Eigen3** - Linear algebra library
- **ZeroMQ** - High-performance messaging library
- **Python3** development headers (for matplotlib integration)

### Optional
- **Ruckig** - Real-time trajectory generation
- **TOPPRA** - Time-optimal path parameterization
- **Doxygen** - API documentation generation
- **Matplotlib** - Plotting capabilities

## Installation

### 1. Install System Dependencies

```bash
# Update package list
sudo apt update

# Install build tools and libraries
sudo apt install -y \
    build-essential \
    cmake \
    libeigen3-dev \
    libzmq3-dev \
    python3-dev \
    python3-numpy \
    python3-matplotlib \
    pkg-config \
    git

# Optional: Install Doxygen for documentation
sudo apt install -y doxygen
```

### 2. Clone Repository

```bash
git clone <your-repository-url>
cd robot_infra
```

### 3. Build Third-party Libraries (Optional)

#### Build Ruckig
```bash
cd 3rdparty
git clone https://github.com/pantor/ruckig.git
cd ruckig
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)
sudo make install
cd ../../..
```

#### Build TOPPRA
```bash
cd 3rdparty
git clone -b develop https://github.com/hungpham2511/toppra.git
cd toppra/cpp
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)
cd ../../../..
```

### 4. Build Project

```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
```

#### Build Options

```bash
# Enable debug mode (uses test_ prefixed FIFO/ZMQ endpoints)
cmake -DENABLE_DEBUG=ON ..

# Build with documentation
cmake -DBUILD_DOCUMENTATION=ON ..
make doc
```

## Usage

### Basic Robot Control

```cpp
#include "industRob.h"

int main() {
    industRob robot;
    
    // Initialize and connect
    robot.connect();
    robot.init();
    
    // Move to joint position
    std::vector<double> joints = {0.1, 0.2, 0.3, 0.0, 0.0, 0.0};
    robot.movAbsJ(joints, 0.5, 0.1, 0.01);
    
    // Wait for completion
    robot.waitRobFree();
    
    return 0;
}
```

### Real-time Servo Control

```cpp
// Enable servo mode
robot.servoMode(1, 10, 1.0, 0.1);  // mode, period(ms), smooth, response

// Send real-time commands
std::vector<double> target_joints = {0.1, 0.2, 0.3, 0.0, 0.0, 0.0};
robot.servoj(target_joints, 0.01, 0.1, 0.5);  // joints, dt, lookahead, gain
```

### Running Tests

```bash
cd build

# Test communication system
./comm_test

# Test hardware simulation
./hw_sim_test

# Test TOPPRA integration
./toppra_fifo_demo

# Run trajectory planning examples
./test/toppra/toppra_example_01
```

## API Reference

### Core Classes

- **`industRob`** - Main robot interface class
- **`ZmqComm`** - ZeroMQ communication handler
- **`TrajectoryPlanner`** - Trajectory generation and planning

### Key Methods

#### Connection & Initialization
- `connect()` - Establish communication
- `disconnect()` - Close communication
- `init()` - Initialize robot system
- `reset()` - Reset robot state

#### Motion Control
- `movAbsJ()` - Absolute joint movement
- `movJ()` - Joint space movement to Cartesian pose
- `movL()` - Linear Cartesian movement
- `servoj()` - Real-time joint servo
- `servoL()` - Real-time Cartesian servo

#### Kinematics
- `robFK()` - Forward kinematics
- `robIk()` - Inverse kinematics

#### Status & Feedback
- `getJoint()` - Get current joint positions
- `getTcpPose()` - Get TCP pose
- `getJointSpeed()` - Get joint velocities
- `getTcpSpeed()` - Get TCP velocity

## Communication Protocol

The system uses ZeroMQ for real-time communication with the following endpoints:

### Real-time Data (1000Hz)
- **Motor State**: `/tmp/data_feedback_fifo` (Robot → SDK)
- **Motion Commands**: `/tmp/data_stream_fifo` (SDK → Robot)

### Command Interface (Non-real-time)
- **Commands**: `/tmp/cmd_set_fifo` (SDK → Robot)
- **Responses**: `/tmp/cmd_feedback_fifo` (Robot → SDK)

### Debug Mode
When `ENABLE_DEBUG=ON`, all endpoints are prefixed with `test_`.

## Development

### Building Documentation

```bash
cd build
cmake -DBUILD_DOCUMENTATION=ON ..
make doc
# Open doc/html/index.html in browser
```

### Adding New Tests

1. Create `.cpp` file in `test/` directory
2. CMake will automatically detect and build it
3. Link against `robot_infra` library

### Code Style

- Follow C++17 standards
- Use meaningful variable names
- Document public APIs with Doxygen comments
- Include error handling for all operations

## Troubleshooting

### Common Issues

1. **ZeroMQ Connection Failed**
   ```bash
   # Check if ZeroMQ is properly installed
   pkg-config --modversion libzmq
   ```

2. **Trajectory Planning Library Not Found**
   ```bash
   # Ensure libraries are built and installed
   sudo ldconfig
   ```

3. **Permission Denied on FIFO**
   ```bash
   # Check FIFO permissions
   ls -la /tmp/*fifo
   ```

### Debug Mode

Enable debug mode to use separate test endpoints:
```bash
cmake -DENABLE_DEBUG=ON ..
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

[Specify your license here]

## Contact

[Your contact information]
