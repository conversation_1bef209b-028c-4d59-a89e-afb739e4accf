







pip install toppra numpy matplotlib








请注意，如果您正在实时平滑数据，则需要找到一种方法，让编译器从数据中选取最后一个F长度样本，以计算瞬态关闭，即x_off。例如，您可以让程序在停止代码后等待x_misers，然后再选取瞬态关闭。


1. **系统初始化**
    1. 通信初始化
    2. 电机回零
    3. 清错复位使能
    4. 运动参数初始化
2. **运动学模块**
    1. 正逆运动学建模
    2. 雅可比矩阵计算（动力学以后）
3. **实时运动指令**
    1. servoJ/L （前期直接PVT，后续迭代   规划层+CSP）
4. **其他指令**
    1. FK/IK
    2. 空间限位
    3. 基于轨迹规划插补的move
5. **状态反馈**
    1. robot_state: 角度，角速度，位姿，速度矢量。时间戳
    2. 伺服状态：是否回零有效，是否使能，是否启用伺服模式，伺服错误码
    3. 机器人 错误码
6. **调试**
    1. 日志系统
    2. 运动轨迹离线保存
    3. 轨迹画图分析

    基于这个结构在ubuntu  C++ 环境生成CMake工程。
    轨迹规划库，依赖ruckig， toppra， reflexxes
    通信依赖ZeroMQ
    运动学依赖pinocchio
    数学依赖Eigen
    画图依赖 matplotlibcpp



通信
zeromq_comm:
    A -- motor_state<br>1kHz --> B
    B -- motor_data<br>100Hz --> A
    A -- ack nort--> B
    B -- cmd nort--> A
    A["servo_board<br>ZMQ_DEALER"]
    B["robot_sdk<br>ZMQ_ROUTER"]
    1.ZeroMQ 和伺服板卡构建进程间两个双向通信，实时双向，非实时双向通信。
    2.板卡实时反馈数据：电机位置，电机速度，时间戳，
    3.robot_sdk 100Hz实时发送 运动指令
    4.robot_sdk 非实时发送命令指令
    5.板卡回复命令返回结果




<NAME_EMAIL>:pantor/ruckig.git
mkdir -p build
cd build
cmake -DCMAKE_BUILD_TYPE=Release -DBUILD_CLOUD_CLIENT=OFF ..

make
sudo make install
sudo ldconfig

export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
接近链接库  与 ros-humble-ruckig冲突
 ./example-01_position 
./example-01_position: symbol lookup error: ./example-01_position: undefined symbol: _ZN6ruckig23PositionFirstOrderStep111get_profileERKNS_7ProfileERNS_5BlockE





经过两轮对比，TOPPRA 非常适合在路径点多且已经进行平滑后使用。而 Ruckig 则非常适合在路径点少的情况下使用，且无需进行进行路径平滑。另外 Ruckig 可以保证轨迹的 jerk 层约束，而 TOPPRA 则无法保证。但是，对于需要进过中间点的情况，只有付费版的 Ruckig Pro 才支持，而 TOPPRA 则是以 MIT 协议完全开源的。在实际使用中， OMPL 不平滑直出的路径具有无碰撞保证，而经过 Bspline 后的轨迹则稍差一些。因此 Ruckig Pro 在 P2P 的时候会略好于 TOPPRA，但如果不想对 Ruckig Pro 付费，那么还是用 TOPPRA。

链接：https://www.zhihu.com/question/612060963/answer/3578059546



# clone
git clone -b develop https://github.com/hungpham2511/toppra
cd toppra
# build
mkdir -p cpp/build && cd cpp/build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)

# run test
./tests/all_tests


输入：

可以选择指定最短持续时间 。请注意，Ruckig 无法保证轨迹的精确持续时间，只能保证最短持续时间。
轨迹持续时间可能被限制为控制周期的倍数。这样，​​在控制回路执行时就能达到精确状态。
using Vector = std::array<double, DOFs>; // By default
 
Vector current_position;
Vector current_velocity; 
Vector current_acceleration; 
 
std::vector<Vector> intermediate_positions; // (only in Pro Version)
std::optional<Vector> min_position; // (only in Pro Version)
std::optional<Vector> max_position; // (only in Pro Version)
TargetLimitViolation target_limit_violation; // 目标状态超出限制时的行为（默认返回错误） (only in Pro Version)
std::optional<double> interrupt_calculation_duration; // [µs], (only in Pro Version)

Vector target_position;
Vector target_velocity; // Initialized to zero
Vector target_acceleration; // Initialized to zero
 
Vector max_velocity;
Vector max_acceleration;
Vector max_jerk; // 初始化为无穷大
 
std::optional<Vector> min_velocity; // 如果未指定，则将使用负的最大速度
std::optional<Vector> min_acceleration; // 如果未给出，则将使用负最大加速度
 

 
std::array<bool, DOFs> enabled; 
std::optional<double> minimum_duration;//可选的最短轨迹持续时间

 
ControlInterface control_interface; // 默认位置接口控制完整的运动状态
Synchronization synchronization; //多个 DoF 的同步行为
DurationDiscretization duration_discretization; // 持续时间是否应为控制周期的离散倍数（默认关闭）

 
std::optional<Vector<ControlInterface>> per_dof_control_interface; // 为每个 DoF 单独设置控制接口，覆盖全局 control_interface
std::optional<Vector<Synchronization>> per_dof_synchronization; // 为每个 DoF 单独设置同步，覆盖全局同步

输出：
Vector new_position;
Vector new_velocity;
Vector new_acceleration;
 
Trajectory trajectory; // 当前轨迹
double time; // 当前自动增加的时间。在新的计算中重置为 0。
 
size_t new_section; // 两个（可能已过滤）中间位置之间的部分的索引。
bool did_section_change; //上次循环是否到达了新的部分？
 
bool new_calculation; // 上一次循环是否进行了新的计算
bool was_calculation_interrupted; // 轨迹计算是否被中断？（仅限专业版）
double calculation_duration; // 最后一个周期的计算持续时间[µs]




参考 comm_zmq.cpp  和 hw_zmq.cpp 已有框架实现。
完成以下需求:
基于zeroMQ /fifo 实现sdk层和底层硬件进程间通信。
其中，通信层为抽象类，zmq fifo 分别完成实现。
fifo  使用四条管道：
const char* CMD_SET_FIFO      = "/tmp/test_cmd_set_fifo";
const char* CMD_FB_FIFO       = "/tmp/test_cmd_feedback_fifo";
const char* STATE_FIFO        = "/tmp/test_data_stream_fifo";
const char* SERVO_CMD_FIFO    = "/tmp/test_data_feedback_fifo";
zmq  使用三条:
static constexpr const char* control_endpoint   = "ipc:///tmp/test_cmd_set_fifo";
static constexpr const char* data_pub_endpoint  = "ipc:///tmp/test_data_feedback_fifo";
static constexpr const char* state_sub_endpoint = "ipc:///tmp/test_data_stream_fifo";

得到以下模块：
comm_base
zmq_comm
fifo_comm

rob_comm 可以简单切换 zmq_comm fifo_comm 。负责封装 
hw_comm 同样切换 zmq_comm fifo_comm ,负责模拟数据，响应

robot_sdk 代码集成rob_comm.

需要实现sdk全部功能代码。以及底层硬件端 接收打印，模拟发送的测试代码。

定义
命令接口	非实时指令	客户端调用接口，等待服务端返回结果。同步执行，指令完成后方可继续下一步。返回值：0 表示成功，其他为错误码。
数据接口	实时指令	客户端周期性下发运动数据，服务端固定周期回传伺服状态（1000Hz）。
服务端（Server）	控制板卡	提供命令接口与实时数据接口。
客户端（Client）	上位机/控制系统	通过接口向板卡下发命令与实时数据。


非实时指令功能（命令接口）:
客户端发送指令给服务端，并阻塞等待返回信息）
命令发送管道 /tmp/cmd_set_fifo，（设定运动状态）
命令返回管道: /tmp/cmd_feedback_fifo


功能	使用场景/功能	接口定义	参数	返回值
系统初始化+使能	系统和轴进行必要的初始和使能	int SYS_INIT();	（预留扩展：配置文件路径）	0：成功其他：错误码
获取伺服状态	推送和主动查询错误	int GET_STATE(int &state);	返回错误状态	同上
执行回零	各轴进行回原点校正	int HOMING();	无	同上
软复位	清除当前软错误，复位到可执行状态	int RESET();	无	同上
查询原点状态	判断是否进行过原点校准，是否可以进行回原点校准	int QUERY_ORIGIN(bool &effective);	返回原点有效性	同上
启动实时伺服	启动 PT 绝对接口运动	int START_SERVO(int mode, double gain, double smooth, int period);	运动模式（PVT/CSP等）、运动增益、轨迹平滑、控制周期(ms)	同上
停止实时伺服	1. 被动报错时2. 主动切换	int STOP_SERVO();	无	同上



实时数据发送：
- 数据发送管道 /tmp/data_stream_fifo，（周期发送运动数据）100-1000Hz

将指令加入到一个内部队列实时发送。

功能	接口定义	说明	返回值
发送运动数据	int SEND_MOTION_DATA(timestamp, motor_pos, motor_vel, motor_time);	实时伺服模式下，固定周期发送运动数据	0成功，其他错误码
数据格式：
字段	编号	类型	示例
时间戳（us）	0	long long	500481387119
电机位置（°）	1-6	double[6]	10 20 30 0 0 0
电机速度（°/s）	7-12	double[6]	20 40 60 0 0 0
运动时长（ms）	13	int	10


实时状态反馈:

- 数据反馈管道 /tmp/data_feedback_fifo，（反馈数据结果）1000Hz
功能	提供外部回调注册	说明
实时数据获取	数据格式参考说明，中间采用空格分割。	初始化之后，一直固定周期回传状态数据
回调数据格式说明：
字段	编号	类型	示例
时间戳（us）	0	long long	500481387119
电机位置（°）	1-6	double[6]	10 20 30 0 0 0
电机速度（°/s）	7-12	double[6]	10 20 30 0 0 0
电机电流	13-18	double[6]	1.1 1.2 1.3 0 0 0
伺服状态码	19	int	100

zmq 参考：
static constexpr const char* control_endpoint   = "ipc:///tmp/test_cmd_set_fifo";
static constexpr const char* data_pub_endpoint  = "ipc:///tmp/test_data_feedback_fifo";
static constexpr const char* state_sub_endpoint = "ipc:///tmp/test_data_stream_fifo";




// 离线计算接口（仅对离线库如TOPP-RA有效，在线库空实现）
    virtual void computeOffline(const std::vector<VectorNd>& waypoints) = 0;

// 单步在线更新接口（仅对在线库如Ruckig有效，离线库空实现）
    virtual bool computeOnline(const VectorNd& current_position,
                                const VectorNd& current_velocity,
                                const VectorNd& current_acceleration,
                                const VectorNd& target_position,
                                const VectorNd& target_velocity,
                                const VectorNd& target_acceleration,
                                double time_step);

离线途径点场景1： 从A点（0,0,0）出发，指定路过B点（0.5，0.5，0.5）。到达C点（1.0，1，1）
在线轨迹场景2：固定周期执行下发目标位置。从C点连续运动到D点。
离线场景3：从D点，路过B点不停留，到达A点。结束。

1 2 3需要衔接。
考虑C++代码特性，使得代码复用，简洁。
可以修改任何文件。我希望能统一出入参。





