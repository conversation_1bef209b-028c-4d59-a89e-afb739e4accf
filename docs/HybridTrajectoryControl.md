# 混合轨迹控制系统

## 概述

混合轨迹控制系统是一个先进的机器人轨迹规划和执行框架，结合了离线轨迹规划（TOPPRA）和在线轨迹跟踪（Ruckig）的优势，实现了高效、平滑、实时的机器人运动控制。

## 核心特性

### 1. 混合控制模式
- **离线规划模式**: 使用TOPPRA进行多航向点的全局最优轨迹规划
- **在线跟踪模式**: 使用Ruckig进行实时轨迹跟踪和动态目标调整
- **混合模式**: 离线规划+在线跟踪，兼具全局最优性和实时响应能力
- **过渡模式**: 平滑的模式切换，确保轨迹连续性
- **紧急模式**: 快速响应紧急情况，安全停止或避障

### 2. 轨迹段管理
- 支持多段轨迹的队列管理
- 动态添加、删除和修改轨迹段
- 轨迹段依赖关系管理
- 多种执行策略（顺序、并行、条件执行）

### 3. 平滑过渡
- 智能过渡轨迹规划
- 多种过渡算法（线性、多项式、样条、时间最优）
- 约束保证和连续性检查
- 可配置的过渡参数

## 系统架构

```
HybridTrajectoryManager
├── HybridTrajectoryPlanner
│   ├── ToppraInterpolator (离线规划)
│   └── RuckigInterpolator (在线跟踪)
├── TrajectorySegmentManager
│   ├── 轨迹段队列管理
│   └── 执行策略控制
└── TransitionPlanner
    ├── 过渡轨迹生成
    └── 平滑算法
```

## 使用场景

### 场景1: 离线规划多航向点轨迹

```cpp
// 创建混合轨迹管理器
auto manager = HybridTrajectoryManagerFactory<6>::createForRobot(
    urdf_path, motion_constraints, 1000.0);

// 定义航向点A->B->C
std::vector<MotionState<6>> waypoints = {point_a, point_b, point_c};

// 切换到混合模式并执行
manager->switchToHybridMode(waypoints);
```

### 场景2: 动态轨迹切换

```cpp
// 在执行过程中切换到新轨迹D->E->F
std::vector<MotionState<6>> new_waypoints = {point_d, point_e, point_f};

// 请求平滑切换
manager->requestTrajectorySwitch(new_waypoints, 0.5); // 0.5秒过渡时间
```

### 场景3: 在线目标跟踪

```cpp
// 切换到在线模式
manager->switchToMode(HybridTrajectoryMode::ONLINE);

// 设置实时目标
MotionState<6> target;
target.position << 1.0, 0.5, -0.3, 0.2, 0.8, -0.1;
manager->setOnlineTarget(target);
```

### 场景4: 紧急停止

```cpp
// 触发紧急停止
manager->emergencyStop();

// 或者紧急过渡到安全位置
MotionState<6> safe_state;
safe_state.position = current_safe_position;
manager->emergencyTransition(safe_state, 1.0); // 1秒内到达安全位置
```

## 核心组件详解

### HybridTrajectoryPlanner

混合轨迹规划器是系统的核心，负责：
- 集成TOPPRA和Ruckig插补器
- 管理轨迹段队列
- 处理轨迹切换和过渡
- 提供统一的目标状态接口

```cpp
template<int DOF>
class HybridTrajectoryPlanner {
public:
    // 添加轨迹段（离线规划）
    bool addTrajectorySegment(const std::vector<MotionState<DOF>>& waypoints, 
                             bool immediate = false);
    
    // 请求轨迹切换
    bool requestTrajectoryTransition(const std::vector<MotionState<DOF>>& new_waypoints, 
                                   double transition_duration = 0.5);
    
    // 获取当前目标状态（在线跟踪）
    TrajectoryState<DOF> getCurrentTarget(double current_time);
    
    // 紧急停止
    bool emergencyStop();
};
```

### TrajectorySegmentManager

轨迹段管理器提供高级的轨迹管理功能：

```cpp
template<int DOF>
class TrajectorySegmentManager {
public:
    // 添加轨迹段
    size_t addSegment(const std::vector<MotionState<DOF>>& waypoints,
                     const SegmentMetadata<DOF>& metadata = {});
    
    // 执行轨迹段
    bool executeSegment(size_t segment_id, bool immediate = false);
    bool executeSequence(const std::vector<size_t>& segment_ids);
    
    // 轨迹切换
    bool switchToSegment(size_t segment_id, double transition_duration = -1);
};
```

### TransitionPlanner

过渡规划器负责生成平滑的过渡轨迹：

```cpp
template<int DOF>
class TransitionPlanner {
public:
    // 规划过渡轨迹
    TransitionResult<DOF> planTransition(const MotionState<DOF>& from_state,
                                        const MotionState<DOF>& to_state,
                                        double desired_duration = -1);
    
    // 紧急过渡
    TransitionResult<DOF> planEmergencyTransition(const MotionState<DOF>& current_state,
                                                 const MotionState<DOF>& safe_state,
                                                 double max_time = 1.0);
};
```

## 配置参数

### 运动约束

```cpp
MotionConstraints<DOF> constraints;
constraints.max_velocity.setConstant(1.0);      // 最大速度 (rad/s)
constraints.max_acceleration.setConstant(2.0);  // 最大加速度 (rad/s²)
constraints.max_jerk.setConstant(10.0);         // 最大加加速度 (rad/s³)
```

### 过渡约束

```cpp
TransitionConstraints<DOF> transition_constraints;
transition_constraints.max_duration = 5.0;        // 最大过渡时间
transition_constraints.min_duration = 0.01;       // 最小过渡时间
transition_constraints.position_tolerance = 1e-3; // 位置容差
transition_constraints.velocity_tolerance = 1e-2; // 速度容差
```

### 管理器配置

```cpp
HybridManagerConfig<DOF> config;
config.control_frequency = 1000.0;              // 控制频率 (Hz)
config.default_transition_duration = 0.5;       // 默认过渡时间 (s)
config.auto_mode_switching = true;              // 自动模式切换
config.enable_emergency_stop = true;            // 启用紧急停止
```

## 实时性能

- **控制频率**: 支持1kHz高频控制
- **轨迹计算**: 离线预计算，在线快速查询
- **内存管理**: 双缓冲机制，无阻塞切换
- **线程安全**: 全面的线程安全保护

## 安全特性

- **约束检查**: 实时监控速度、加速度、加加速度约束
- **紧急停止**: 快速响应紧急情况
- **状态验证**: 输入状态的有效性检查
- **错误恢复**: 自动错误检测和恢复机制

## 编译和使用

### 依赖项
- Eigen3 (矩阵运算)
- Ruckig (在线轨迹生成)
- TOPPRA (离线轨迹优化)
- C++17 编译器

### 编译示例
```bash
cd examples
mkdir build && cd build
cmake ..
make -j4
```

### 运行示例
```bash
# 完整功能演示
./hybrid_trajectory_example

# 简单组件测试
./simple_hybrid_test
```

## 最佳实践

1. **约束设置**: 根据机器人实际能力设置合理的运动约束
2. **过渡时间**: 平衡响应速度和运动平滑性
3. **轨迹段长度**: 避免过短或过长的轨迹段
4. **错误处理**: 实现完善的错误处理和恢复机制
5. **性能监控**: 监控实时性能指标，确保满足控制要求

## 扩展开发

系统采用模板化设计，支持任意自由度的机器人。主要扩展点：

- 自定义过渡算法
- 新的执行策略
- 特殊约束处理
- 多机器人协调

## 故障排除

常见问题和解决方案：

1. **轨迹规划失败**: 检查约束设置和航向点有效性
2. **过渡不平滑**: 调整过渡时间和算法参数
3. **实时性能问题**: 优化轨迹段大小和控制频率
4. **约束违反**: 放宽约束或调整轨迹参数
