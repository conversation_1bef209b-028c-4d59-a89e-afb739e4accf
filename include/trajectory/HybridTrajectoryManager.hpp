#ifndef HYBRID_TRAJECTORY_MANAGER_HPP
#define HYBRID_TRAJECTORY_MANAGER_HPP

#include "HybridTrajectoryPlanner.hpp"
#include "TrajectorySegmentManager.hpp"
#include "TransitionPlanner.hpp"
#include "TrajInterpolatorBase.hpp"
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <functional>

// 扩展的轨迹模式，包含混合模式
enum class HybridTrajectoryMode {
    OFFLINE,              // 纯离线模式（TOPPRA）
    ONLINE,               // 纯在线模式（Ruckig）
    HYBRID,               // 混合模式（离线规划+在线跟踪）
    TRANSITION,           // 过渡状态
    IDLE,                 // 空闲状态
    EMERGENCY             // 紧急状态
};

// 混合轨迹管理器配置
template<int DOF>
struct HybridManagerConfig {
    double control_frequency = 1000.0;  // 控制频率
    double dt = 0.001;                  // 时间步长
    
    // 模式切换参数
    double default_transition_duration = 0.5;
    bool auto_mode_switching = true;
    bool enable_emergency_stop = true;
    
    // 性能参数
    size_t max_trajectory_buffer_size = 10000;
    double trajectory_lookahead_time = 0.1;
    
    // 约束参数
    MotionConstraints<DOF> motion_constraints;
    TransitionConstraints<DOF> transition_constraints;
    
    HybridManagerConfig() = default;
};

// 混合轨迹管理器事件
template<int DOF>
struct HybridManagerEvent {
    enum Type {
        MODE_CHANGED,
        SEGMENT_COMPLETED,
        TRAJECTORY_UPDATED,
        TRANSITION_STARTED,
        TRANSITION_COMPLETED,
        ERROR_OCCURRED,
        EMERGENCY_TRIGGERED
    };
    
    Type type;
    HybridTrajectoryMode from_mode;
    HybridTrajectoryMode to_mode;
    size_t segment_id = 0;
    std::string message;
    double timestamp;
    
    HybridManagerEvent(Type t, const std::string& msg = "") 
        : type(t), message(msg), timestamp(0.0) {}
};

// 混合轨迹管理器
template<int DOF>
class HybridTrajectoryManager {
public:
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    using PlannerPtr = std::shared_ptr<HybridTrajectoryPlanner<DOF>>;
    using SegmentManagerPtr = std::shared_ptr<TrajectorySegmentManager<DOF>>;
    using TransitionPlannerPtr = std::shared_ptr<TransitionPlanner<DOF>>;
    using EventCallback = std::function<void(const HybridManagerEvent<DOF>&)>;
    using StateCallback = std::function<void(const TrajectoryState<DOF>&)>;

private:
    // 核心组件
    PlannerPtr hybrid_planner_;
    SegmentManagerPtr segment_manager_;
    TransitionPlannerPtr transition_planner_;
    
    // 状态管理
    std::atomic<HybridTrajectoryMode> current_mode_{HybridTrajectoryMode::IDLE};
    std::atomic<bool> is_running_{false};
    std::atomic<bool> emergency_stop_{false};
    MotionState<DOF> current_state_;
    mutable std::mutex state_mutex_;
    
    // 时间管理
    std::atomic<double> current_time_{0.0};
    std::atomic<double> start_time_{0.0};
    
    // 配置
    HybridManagerConfig<DOF> config_;
    std::string urdf_path_;
    
    // 回调函数
    EventCallback event_callback_;
    StateCallback state_callback_;
    
    // 线程管理
    std::thread control_thread_;
    std::atomic<bool> control_thread_running_{false};

public:
    explicit HybridTrajectoryManager(const std::string& urdf_path = "", 
                                   const HybridManagerConfig<DOF>& config = HybridManagerConfig<DOF>())
        : urdf_path_(urdf_path), config_(config) {
        
        // 创建核心组件
        hybrid_planner_ = std::make_shared<HybridTrajectoryPlanner<DOF>>(config_.dt, urdf_path);
        segment_manager_ = std::make_shared<TrajectorySegmentManager<DOF>>(hybrid_planner_);
        transition_planner_ = std::make_shared<TransitionPlanner<DOF>>(config_.dt, urdf_path);
        
        // 设置约束
        hybrid_planner_->setConstraints(config_.motion_constraints);
        transition_planner_->setConstraints(config_.transition_constraints);
        
        // 设置回调
        setupCallbacks();
    }

    virtual ~HybridTrajectoryManager() {
        stop();
    }

    // 初始化和控制
    bool initialize();
    bool start();
    void stop();
    void pause();
    void resume();
    
    // 状态查询
    bool isRunning() const { return is_running_.load(); }
    HybridTrajectoryMode getCurrentMode() const { return current_mode_.load(); }
    MotionState<DOF> getCurrentState() const;
    double getCurrentTime() const { return current_time_.load(); }
    bool isEmergencyStop() const { return emergency_stop_.load(); }

    // 轨迹管理接口
    size_t addTrajectorySegment(const std::vector<MotionState<DOF>>& waypoints,
                               const SegmentMetadata<DOF>& metadata = SegmentMetadata<DOF>());
    
    bool executeSegment(size_t segment_id, bool immediate = false);
    bool executeSequence(const std::vector<size_t>& segment_ids);
    
    // 模式切换
    bool switchToMode(HybridTrajectoryMode mode, double transition_duration = -1);
    bool switchToHybridMode(const std::vector<MotionState<DOF>>& waypoints);
    
    // 在线控制
    bool setOnlineTarget(const MotionState<DOF>& target_state);
    bool addOnlineWaypoint(const MotionState<DOF>& waypoint);
    
    // 轨迹切换
    bool requestTrajectorySwitch(const std::vector<MotionState<DOF>>& new_waypoints,
                               double transition_duration = -1);
    
    // 紧急控制
    bool emergencyStop();
    bool emergencyTransition(const MotionState<DOF>& safe_state, double max_time = 1.0);
    void clearEmergency();
    
    // 配置管理
    void setConfig(const HybridManagerConfig<DOF>& config);
    const HybridManagerConfig<DOF>& getConfig() const { return config_; }
    
    void updateConstraints(const MotionConstraints<DOF>& constraints);
    
    // 回调设置
    void setEventCallback(const EventCallback& callback) { event_callback_ = callback; }
    void setStateCallback(const StateCallback& callback) { state_callback_ = callback; }
    
    // 状态更新（外部调用）
    void updateCurrentState(const MotionState<DOF>& state);
    void updateTime(double dt);
    
    // 获取组件访问
    PlannerPtr getHybridPlanner() const { return hybrid_planner_; }
    SegmentManagerPtr getSegmentManager() const { return segment_manager_; }
    TransitionPlannerPtr getTransitionPlanner() const { return transition_planner_; }
    
    // 统计信息
    size_t getTotalSegments() const;
    size_t getActiveSegments() const;
    double getTotalTrajectoryDuration() const;
    
    // 调试和监控
    void printStatus() const;
    std::string getStatusString() const;

private:
    // 内部方法
    void setupCallbacks();
    void controlLoop();
    
    // 模式处理
    TrajectoryState<DOF> processCurrentMode();
    TrajectoryState<DOF> processOfflineMode();
    TrajectoryState<DOF> processOnlineMode();
    TrajectoryState<DOF> processHybridMode();
    TrajectoryState<DOF> processTransitionMode();
    TrajectoryState<DOF> processEmergencyMode();
    
    // 事件处理
    void onSegmentCompleted(size_t segment_id);
    void onTransitionCompleted();
    void onError(const std::string& error);
    
    // 模式切换逻辑
    bool performModeTransition(HybridTrajectoryMode target_mode, double transition_duration);
    bool validateModeTransition(HybridTrajectoryMode from_mode, HybridTrajectoryMode to_mode) const;
    
    // 安全检查
    bool validateState(const MotionState<DOF>& state) const;
    bool checkConstraints(const TrajectoryState<DOF>& state) const;
    
    // 事件通知
    void notifyEvent(const HybridManagerEvent<DOF>& event);
    void notifyStateUpdate(const TrajectoryState<DOF>& state);
    
    // 时间管理
    void resetTime();
    double getElapsedTime() const;
};

// 混合轨迹管理器工厂
template<int DOF>
class HybridTrajectoryManagerFactory {
public:
    static std::shared_ptr<HybridTrajectoryManager<DOF>> create(
        const std::string& urdf_path = "",
        double control_frequency = 1000.0) {
        
        HybridManagerConfig<DOF> config;
        config.control_frequency = control_frequency;
        config.dt = 1.0 / control_frequency;
        
        return std::make_shared<HybridTrajectoryManager<DOF>>(urdf_path, config);
    }
    
    static std::shared_ptr<HybridTrajectoryManager<DOF>> createForRobot(
        const std::string& urdf_path,
        const MotionConstraints<DOF>& constraints,
        double control_frequency = 1000.0) {
        
        HybridManagerConfig<DOF> config;
        config.control_frequency = control_frequency;
        config.dt = 1.0 / control_frequency;
        config.motion_constraints = constraints;
        
        // 设置过渡约束
        config.transition_constraints.max_velocity = constraints.max_velocity;
        config.transition_constraints.max_acceleration = constraints.max_acceleration;
        config.transition_constraints.max_jerk = constraints.max_jerk;
        
        return std::make_shared<HybridTrajectoryManager<DOF>>(urdf_path, config);
    }
};

#endif // HYBRID_TRAJECTORY_MANAGER_HPP
