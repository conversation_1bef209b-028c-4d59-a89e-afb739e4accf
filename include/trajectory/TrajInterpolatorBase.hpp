#ifndef TRAJ_INTERPOLATOR_BASE_H
#define TRAJ_INTERPOLATOR_BASE_H

#include <Eigen/Dense>
#include <vector>
#include <string>
#include <mutex>
#include <memory>
#include <chrono>
#include <iostream>
#include <iomanip>


// Motion constraints
template<int DOF>
struct MotionConstraints {
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    
    VectorDOF max_velocity;
    VectorDOF max_acceleration;
    VectorDOF max_jerk;
    
    MotionConstraints() {
        max_velocity.setOnes();
        max_acceleration.setOnes() * 2.0;
        max_jerk.setOnes() * 10.0;
    }
};

// Waypoint structure for trajectory planning
template<int DOF>
struct  MotionState {
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;

    VectorDOF position;
    VectorDOF velocity;
    VectorDOF acceleration;

    MotionState() {
        position.setZero();
        velocity.setZero();
        acceleration.setZero();
    }

    MotionState(const VectorDOF& pos, const VectorDOF& vel = VectorDOF::Zero(),
             const VectorDOF& acc = VectorDOF::Zero())
        : position(pos), velocity(vel), acceleration(acc){}
};


// Trajectory state for any DOF
template<int DOF>
struct TrajectoryState {
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    
    VectorDOF position = VectorDOF::Zero();
    VectorDOF velocity = VectorDOF::Zero();
    VectorDOF acceleration = VectorDOF::Zero();
    double timestamp = 0.0;
    bool valid = false;
    
    TrajectoryState() = default;
    TrajectoryState(const VectorDOF& pos, const VectorDOF& vel = VectorDOF::Zero(), 
                   const VectorDOF& acc = VectorDOF::Zero(), double t = 0.0)
        : position(pos), velocity(vel), acceleration(acc), timestamp(t), valid(true) {}
};

// Thread-safe trajectory buffer
template<int DOF>
class TrajectoryBuffer {
public:
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;

private:
    std::vector<TrajectoryState<DOF>> states_;
    std::vector<double> timestamps_;
    mutable std::mutex mutex_;
    size_t current_index_ = 0;
    double start_time_ = 0.0;
    bool is_looping_ = false;

public:
    // Set trajectory data
    void setTrajectory(const std::vector<TrajectoryState<DOF>>& states,
                      const std::vector<double>& timestamps, bool loop = false) {
        std::lock_guard<std::mutex> lock(mutex_);
        states_ = states;
        timestamps_ = timestamps;
        is_looping_ = loop;
        current_index_ = 0;
        if (!timestamps_.empty()) {
            start_time_ = timestamps_[0];
        }
    }

    // Get state at specific time with interpolation
    TrajectoryState<DOF> getStateAtTime(double time) const {
        std::lock_guard<std::mutex> lock(mutex_);

        if (states_.empty()) {
            return TrajectoryState<DOF>();
        }

        // Handle time before trajectory start
        if (time <= timestamps_[0]) {
            return states_[0];
        }

        // Handle time after trajectory end
        if (time >= timestamps_.back()) {
            if (is_looping_) {
                double duration = timestamps_.back() - timestamps_[0];
                double loop_time = std::fmod(time - timestamps_[0], duration) + timestamps_[0];
                return getStateAtTimeInternal(loop_time);
            } else {
                return states_.back();
            }
        }

        return getStateAtTimeInternal(time);
    }

    // Update current index for efficiency
    void updateIndex(double current_time) {
        std::lock_guard<std::mutex> lock(mutex_);

        if (timestamps_.empty()) return;

        // Find appropriate index
        while (current_index_ < timestamps_.size() - 1 &&
               current_time > timestamps_[current_index_ + 1]) {
            current_index_++;
        }

        while (current_index_ > 0 &&
               current_time < timestamps_[current_index_]) {
            current_index_--;
        }
    }

    bool isEmpty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return states_.empty();
    }

    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        states_.clear();
        timestamps_.clear();
        current_index_ = 0;
        start_time_ = 0.0;
        is_looping_ = false;
    }

    double getDuration() const {
        std::lock_guard<std::mutex> lock(mutex_);
        if (timestamps_.empty()) return 0.0;
        return timestamps_.back() - timestamps_[0];
    }

    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return states_.size();
    }

    // Sample and print trajectory at regular intervals
    void sampleAndPrint(double dt = 0.01, int max_samples = -1, int precision = 4) const {
        std::lock_guard<std::mutex> lock(mutex_);

        if (states_.empty()) {
            std::cout << "Trajectory buffer is empty" << std::endl;
            return;
        }

        // Calculate duration without calling getDuration() to avoid deadlock
        double duration = timestamps_.empty() ? 0.0 : (timestamps_.back() - timestamps_[0]);
        int num_samples = static_cast<int>(duration / dt) + 1;

        // Limit samples if specified
        if (max_samples > 0 && num_samples > max_samples) {
            num_samples = max_samples;
        }

        // Set output precision
        std::cout << std::fixed << std::setprecision(precision);
        std::cout << "Trajectory samples (dt=" << dt << "s):" << std::endl;
        std::cout << "Buffer size: " << states_.size() << " states" << std::endl;
        std::cout << "Duration: " << duration << " seconds" << std::endl;

        // Sample trajectory
        for (int i = 0; i < num_samples; ++i) {
            double t = std::min(i * dt, duration);
            TrajectoryState<DOF> state = getStateAtTimeInternal(t);

            if (state.valid) {
                std::cout << "t=" << t
                          << ", pos=[" << state.position.transpose() << "]"
                          << ", vel=[" << state.velocity.transpose() << "]"
                          << ", acc=[" << state.acceleration.transpose() << "]" << std::endl;
            }
        }
    }

    // Sample and print with limited output (first N and last N samples)
    void sampleAndPrintLimited(double dt = 0.01, int show_count = 10, int precision = 4) const {
        std::lock_guard<std::mutex> lock(mutex_);

        if (states_.empty()) {
            std::cout << "Trajectory buffer is empty" << std::endl;
            return;
        }

        // Calculate duration without calling getDuration() to avoid deadlock
        double duration = timestamps_.empty() ? 0.0 : (timestamps_.back() - timestamps_[0]);
        int num_samples = static_cast<int>(duration / dt) + 1;
        // Set output precision
        std::cout << std::fixed << std::setprecision(precision);
        std::cout << "Trajectory samples (dt=" << dt << "s, showing first " << show_count
                  << " and last " << show_count << "):" << std::endl;
        std::cout << "Buffer size: " << states_.size() << " states" << std::endl;
        std::cout << "Duration: " << duration << " seconds" << std::endl;
        std::cout << "Total samples: " << num_samples << std::endl;

        // Sample trajectory with limited output
        for (int i = 0; i < num_samples; ++i) {
            // Only print first show_count and last show_count samples
            if (i < show_count || i >= num_samples - show_count) {
                double t = std::min(i * dt, duration);
                TrajectoryState<DOF> state = getStateAtTimeInternal(t);

                if (state.valid) {
                    std::cout << "t=" << t
                              << ", pos=[" << state.position.transpose() << "]"
                              << ", vel=[" << state.velocity.transpose() << "]"
                              << ", acc=[" << state.acceleration.transpose() << "]" << std::endl;
                }
            } else if (i == show_count) {
                std::cout << "... (skipping " << (num_samples - 2 * show_count)
                          << " intermediate samples) ..." << std::endl;
            }
        }
    }

private:
    // Internal interpolation function (assumes mutex is already locked)
    TrajectoryState<DOF> getStateAtTimeInternal(double time) const {
        // Binary search for the right interval
        auto it = std::lower_bound(timestamps_.begin(), timestamps_.end(), time);

        if (it == timestamps_.end()) {
            return states_.back();
        }

        size_t idx = std::distance(timestamps_.begin(), it);

        if (idx == 0 || timestamps_[idx] == time) {
            return states_[idx];
        }

        // Linear interpolation between states_[idx-1] and states_[idx]
        double t1 = timestamps_[idx - 1];
        double t2 = timestamps_[idx];
        double alpha = (time - t1) / (t2 - t1);

        TrajectoryState<DOF> result;
        result.position = (1.0 - alpha) * states_[idx - 1].position + alpha * states_[idx].position;
        result.velocity = (1.0 - alpha) * states_[idx - 1].velocity + alpha * states_[idx].velocity;
        result.acceleration = (1.0 - alpha) * states_[idx - 1].acceleration + alpha * states_[idx].acceleration;
        result.timestamp = time;
        result.valid = states_[idx - 1].valid && states_[idx].valid;

        return result;
    }
};

// Base class for trajectory interpolators
template<int DOF>
class TrajectoryInterpolator {
public:
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    using MatrixDOF = Eigen::Matrix<double, DOF, Eigen::Dynamic>;

protected:
    double dt_ = 0.001;  // trajectory interpolation step (e.g., 1ms)
    std::string urdf_path_;
    MotionConstraints<DOF> constraints_;
    TrajectoryBuffer<DOF> trajectory_buffer_;
    std::string last_error_;  // Added missing member variable

public:
    // Constructor with time step
    explicit TrajectoryInterpolator(double dt = 0.001,std::string urdf_path = "") : dt_(dt),urdf_path_(urdf_path) {}

    virtual ~TrajectoryInterpolator() = default;

    // Set motion constraints
    virtual void setConstraints(const MotionConstraints<DOF>& constraints) {
        constraints_ = constraints;
    }

    // Get current constraints
    const MotionConstraints<DOF>& getConstraints() const {
        return constraints_;
    }

    // Offline trajectory planning (multiple MotionState)
    virtual bool computeOffline(const std::vector<MotionState<DOF>>& waypoints) = 0;

    // Online trajectory planning (current state -> target state)
    virtual bool computeOnline(const MotionState<DOF>& current_state,
                               const MotionState<DOF>& target_state) = 0;

    // Get trajectory buffer (const)
    virtual const TrajectoryBuffer<DOF>& getTrajectoryBuffer() const {
        return trajectory_buffer_;
    }

    // Get state at specific time
    virtual TrajectoryState<DOF> getStateAtTime(double time) const {
        return trajectory_buffer_.getStateAtTime(time);
    }

    // Get trajectory duration
    virtual double getDuration() const {
        return trajectory_buffer_.getDuration();
    }

    // Check if trajectory is valid
    virtual bool isValid() const {
        return !trajectory_buffer_.isEmpty();
    }

    // Get interpolation time step
    double getTimeStep() const {
        return dt_;
    }

    // Set interpolation time step
    void setTimeStep(double dt) {
        dt_ = dt;
    }

    // Get last error message
    const std::string& getLastError() const {
        return last_error_;
    }

    // Clear trajectory
    virtual void clear() {
        trajectory_buffer_.clear();
        last_error_.clear();
    }

    // Get interpolator type name (for debugging)
    virtual std::string getTypeName() const = 0;

protected:
    // Set error message
    void setError(const std::string& error) {
        last_error_ = error;
    }

    // Clear error
    void clearError() {
        last_error_.clear();
    }
};

#endif // TRAJ_INTERPOLATOR_BASE_H