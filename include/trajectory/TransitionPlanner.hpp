#ifndef TRANSITION_PLANNER_HPP
#define TRANSITION_PLANNER_HPP

#include "TrajInterpolatorBase.hpp"
#include "RuckigInterpolator.hpp"
#include <memory>
#include <vector>
#include <functional>

// 过渡类型枚举
enum class TransitionType {
    LINEAR,          // 线性过渡
    POLYNOMIAL,      // 多项式过渡
    SPLINE,          // 样条过渡
    OPTIMAL_TIME,    // 时间最优过渡
    SMOOTH_JERK      // 平滑加加速度过渡
};

// 过渡约束
template<int DOF>
struct TransitionConstraints {
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    
    VectorDOF max_velocity = VectorDOF::Ones() * 1.0;
    VectorDOF max_acceleration = VectorDOF::Ones() * 1.0;
    VectorDOF max_jerk = VectorDOF::Ones() * 10.0;
    
    double max_duration = 5.0;        // 最大过渡时间
    double min_duration = 0.01;       // 最小过渡时间
    double position_tolerance = 1e-3; // 位置容差
    double velocity_tolerance = 1e-2; // 速度容差
    
    TransitionConstraints() = default;
};

// 过渡配置
struct TransitionConfig {
    TransitionType type = TransitionType::OPTIMAL_TIME;
    bool ensure_continuity = true;     // 确保连续性
    bool minimize_time = true;         // 最小化时间
    bool minimize_jerk = false;        // 最小化加加速度
    double smoothness_factor = 1.0;    // 平滑因子
    
    TransitionConfig() = default;
};

// 过渡结果
template<int DOF>
struct TransitionResult {
    bool success = false;
    double duration = 0.0;
    std::vector<TrajectoryState<DOF>> trajectory_points;
    std::vector<double> timestamps;
    std::string error_message;
    
    // 质量指标
    double max_velocity_violation = 0.0;
    double max_acceleration_violation = 0.0;
    double max_jerk_violation = 0.0;
    double smoothness_score = 0.0;
    
    TransitionResult() = default;
};

// 过渡轨迹规划器
template<int DOF>
class TransitionPlanner {
public:
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    using ProgressCallback = std::function<void(double progress)>;

private:
    // 核心插补器
    std::unique_ptr<RuckigInterpolator<DOF>> ruckig_planner_;
    
    // 配置参数
    double dt_ = 0.001;
    TransitionConstraints<DOF> constraints_;
    TransitionConfig config_;
    
    // 回调函数
    ProgressCallback progress_callback_;

public:
    explicit TransitionPlanner(double dt = 0.001, const std::string& urdf_path = "")
        : dt_(dt) {
        ruckig_planner_ = std::make_unique<RuckigInterpolator<DOF>>(dt, urdf_path);
    }

    virtual ~TransitionPlanner() = default;

    // 设置约束和配置
    void setConstraints(const TransitionConstraints<DOF>& constraints) {
        constraints_ = constraints;
        
        // 更新Ruckig约束
        MotionConstraints<DOF> ruckig_constraints;
        ruckig_constraints.max_velocity = constraints.max_velocity;
        ruckig_constraints.max_acceleration = constraints.max_acceleration;
        ruckig_constraints.max_jerk = constraints.max_jerk;
        ruckig_planner_->setConstraints(ruckig_constraints);
    }

    void setConfig(const TransitionConfig& config) {
        config_ = config;
    }

    void setProgressCallback(const ProgressCallback& callback) {
        progress_callback_ = callback;
    }

    // 主要规划接口
    TransitionResult<DOF> planTransition(const MotionState<DOF>& from_state,
                                        const MotionState<DOF>& to_state,
                                        double desired_duration = -1);

    // 多段过渡规划
    TransitionResult<DOF> planMultiSegmentTransition(
        const std::vector<MotionState<DOF>>& waypoints,
        const std::vector<double>& segment_durations = {});

    // 轨迹连接
    TransitionResult<DOF> connectTrajectories(
        const std::vector<TrajectoryState<DOF>>& traj1,
        const std::vector<TrajectoryState<DOF>>& traj2,
        double connection_duration = -1);

    // 轨迹平滑
    TransitionResult<DOF> smoothTrajectory(
        const std::vector<TrajectoryState<DOF>>& input_trajectory,
        double smoothing_window = 0.1);

    // 紧急过渡（快速停止或避障）
    TransitionResult<DOF> planEmergencyTransition(
        const MotionState<DOF>& current_state,
        const MotionState<DOF>& safe_state,
        double max_time = 1.0);

    // 获取配置
    const TransitionConstraints<DOF>& getConstraints() const { return constraints_; }
    const TransitionConfig& getConfig() const { return config_; }

private:
    // 内部规划方法
    TransitionResult<DOF> planLinearTransition(const MotionState<DOF>& from_state,
                                              const MotionState<DOF>& to_state,
                                              double duration);

    TransitionResult<DOF> planPolynomialTransition(const MotionState<DOF>& from_state,
                                                  const MotionState<DOF>& to_state,
                                                  double duration);

    TransitionResult<DOF> planSplineTransition(const MotionState<DOF>& from_state,
                                              const MotionState<DOF>& to_state,
                                              double duration);

    TransitionResult<DOF> planOptimalTimeTransition(const MotionState<DOF>& from_state,
                                                   const MotionState<DOF>& to_state);

    TransitionResult<DOF> planSmoothJerkTransition(const MotionState<DOF>& from_state,
                                                  const MotionState<DOF>& to_state,
                                                  double duration);

    // 辅助方法
    double estimateOptimalDuration(const MotionState<DOF>& from_state,
                                  const MotionState<DOF>& to_state) const;

    bool validateTransition(const MotionState<DOF>& from_state,
                           const MotionState<DOF>& to_state) const;

    void evaluateTrajectoryQuality(TransitionResult<DOF>& result) const;

    bool checkConstraintViolations(const std::vector<TrajectoryState<DOF>>& trajectory,
                                  TransitionResult<DOF>& result) const;

    void sampleTrajectory(const std::vector<TrajectoryState<DOF>>& dense_trajectory,
                         double sample_dt,
                         std::vector<TrajectoryState<DOF>>& sampled_trajectory,
                         std::vector<double>& timestamps) const;

    // 数值方法
    VectorDOF computePolynomialCoefficients(const MotionState<DOF>& from_state,
                                           const MotionState<DOF>& to_state,
                                           double duration) const;

    TrajectoryState<DOF> evaluatePolynomial(const VectorDOF& coeffs,
                                           double t, double duration) const;

    // 平滑算法
    std::vector<TrajectoryState<DOF>> applySavitzkyGolayFilter(
        const std::vector<TrajectoryState<DOF>>& trajectory,
        int window_size = 5, int poly_order = 3) const;

    std::vector<TrajectoryState<DOF>> applyGaussianSmoothing(
        const std::vector<TrajectoryState<DOF>>& trajectory,
        double sigma = 1.0) const;
};

// 过渡规划器工厂
template<int DOF>
class TransitionPlannerFactory {
public:
    static std::unique_ptr<TransitionPlanner<DOF>> create(
        TransitionType type = TransitionType::OPTIMAL_TIME,
        double dt = 0.001,
        const std::string& urdf_path = "") {
        
        auto planner = std::make_unique<TransitionPlanner<DOF>>(dt, urdf_path);
        
        TransitionConfig config;
        config.type = type;
        planner->setConfig(config);
        
        return planner;
    }
    
    static std::unique_ptr<TransitionPlanner<DOF>> createForEmergency(
        double dt = 0.001,
        const std::string& urdf_path = "") {
        
        auto planner = create(TransitionType::OPTIMAL_TIME, dt, urdf_path);
        
        TransitionConfig config;
        config.type = TransitionType::OPTIMAL_TIME;
        config.minimize_time = true;
        config.ensure_continuity = false;  // 紧急情况下可以牺牲连续性
        planner->setConfig(config);
        
        return planner;
    }
};

#endif // TRANSITION_PLANNER_HPP
