#ifndef TRAJECTORY_SEGMENT_MANAGER_HPP
#define TRAJECTORY_SEGMENT_MANAGER_HPP

#include "HybridTrajectoryPlanner.hpp"
#include <memory>
#include <vector>
#include <map>
#include <mutex>
#include <atomic>
#include <functional>

// 段执行策略
enum class SegmentExecutionMode {
    SEQUENTIAL,    // 顺序执行
    PARALLEL,      // 并行执行（多机器人）
    CONDITIONAL    // 条件执行
};

// 段连接类型
enum class SegmentConnectionType {
    SMOOTH,        // 平滑连接
    STOP_AND_GO,   // 停止后继续
    DIRECT         // 直接连接
};

// 轨迹段元数据
template<int DOF>
struct SegmentMetadata {
    size_t segment_id;
    std::string name;
    std::string description;
    SegmentExecutionMode execution_mode = SegmentExecutionMode::SEQUENTIAL;
    SegmentConnectionType connection_type = SegmentConnectionType::SMOOTH;
    double priority = 1.0;
    std::vector<size_t> dependencies;  // 依赖的段ID
    std::map<std::string, double> parameters;  // 自定义参数
    
    SegmentMetadata(size_t id = 0, const std::string& n = "") 
        : segment_id(id), name(n) {}
};

// 轨迹段管理器
template<int DOF>
class TrajectorySegmentManager {
public:
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;
    using SegmentPtr = std::shared_ptr<TrajectorySegment<DOF>>;
    using PlannerPtr = std::shared_ptr<HybridTrajectoryPlanner<DOF>>;
    using SegmentCallback = std::function<void(size_t segment_id, SegmentStatus status)>;
    using ErrorCallback = std::function<void(const std::string& error)>;

private:
    // 核心组件
    PlannerPtr hybrid_planner_;
    
    // 段管理
    std::map<size_t, SegmentPtr> segments_;
    std::map<size_t, SegmentMetadata<DOF>> metadata_;
    std::vector<size_t> execution_order_;
    mutable std::mutex segments_mutex_;
    
    // 状态管理
    std::atomic<bool> is_running_{false};
    std::atomic<size_t> current_segment_id_{0};
    std::atomic<size_t> next_id_{1};
    
    // 回调函数
    SegmentCallback segment_callback_;
    ErrorCallback error_callback_;
    
    // 配置
    double default_transition_duration_ = 0.5;
    bool auto_execute_ = true;

public:
    explicit TrajectorySegmentManager(PlannerPtr planner = nullptr)
        : hybrid_planner_(planner) {
        if (!hybrid_planner_) {
            hybrid_planner_ = std::make_shared<HybridTrajectoryPlanner<DOF>>();
        }
        
        // 设置规划器回调
        hybrid_planner_->setCompletionCallback(
            [this](size_t segment_id) { onSegmentCompleted(segment_id); });
    }

    virtual ~TrajectorySegmentManager() = default;

    // 段管理接口
    size_t addSegment(const std::vector<MotionState<DOF>>& waypoints,
                     const SegmentMetadata<DOF>& metadata = SegmentMetadata<DOF>());
    
    bool removeSegment(size_t segment_id);
    bool updateSegment(size_t segment_id, const std::vector<MotionState<DOF>>& waypoints);
    
    // 执行控制
    bool executeSegment(size_t segment_id, bool immediate = false);
    bool executeSequence(const std::vector<size_t>& segment_ids);
    bool executeAll();
    
    // 轨迹切换
    bool switchToSegment(size_t segment_id, double transition_duration = -1);
    bool insertSegment(const std::vector<MotionState<DOF>>& waypoints, 
                      size_t after_segment_id = 0);
    
    // 状态查询
    SegmentPtr getSegment(size_t segment_id) const;
    SegmentPtr getCurrentSegment() const;
    SegmentMetadata<DOF> getMetadata(size_t segment_id) const;
    std::vector<size_t> getAllSegmentIds() const;
    std::vector<size_t> getPendingSegments() const;
    size_t getCurrentSegmentId() const { return current_segment_id_.load(); }
    size_t getQueueSize() const;
    
    // 控制接口
    bool start();
    void stop();
    void pause();
    void resume();
    bool isRunning() const { return is_running_.load(); }
    
    // 配置
    void setAutoExecute(bool enable) { auto_execute_ = enable; }
    void setDefaultTransitionDuration(double duration) { 
        default_transition_duration_ = duration; 
    }
    
    // 回调设置
    void setSegmentCallback(const SegmentCallback& callback) {
        segment_callback_ = callback;
    }
    
    void setErrorCallback(const ErrorCallback& callback) {
        error_callback_ = callback;
    }
    
    // 获取底层规划器
    PlannerPtr getHybridPlanner() const { return hybrid_planner_; }
    
    // 清理
    void clear();
    
    // 统计信息
    size_t getTotalSegments() const;
    size_t getCompletedSegments() const;
    double getTotalDuration() const;

private:
    // 内部方法
    void onSegmentCompleted(size_t segment_id);
    bool validateSegmentDependencies(size_t segment_id) const;
    std::vector<size_t> resolveDependencies(const std::vector<size_t>& segment_ids) const;
    bool checkSegmentConnection(size_t from_id, size_t to_id) const;
    
    void notifySegmentStatus(size_t segment_id, SegmentStatus status);
    void notifyError(const std::string& error);
    
    // 执行策略
    bool executeSequential(const std::vector<size_t>& segment_ids);
    bool executeParallel(const std::vector<size_t>& segment_ids);
    bool executeConditional(const std::vector<size_t>& segment_ids);
};

// 轨迹段构建器（Builder模式）
template<int DOF>
class SegmentBuilder {
private:
    std::vector<MotionState<DOF>> waypoints_;
    SegmentMetadata<DOF> metadata_;

public:
    SegmentBuilder& addWaypoint(const MotionState<DOF>& waypoint) {
        waypoints_.push_back(waypoint);
        return *this;
    }
    
    SegmentBuilder& addWaypoint(const Eigen::Matrix<double, DOF, 1>& position,
                               const Eigen::Matrix<double, DOF, 1>& velocity = Eigen::Matrix<double, DOF, 1>::Zero()) {
        MotionState<DOF> state;
        state.position = position;
        state.velocity = velocity;
        state.acceleration = Eigen::Matrix<double, DOF, 1>::Zero();
        waypoints_.push_back(state);
        return *this;
    }
    
    SegmentBuilder& setName(const std::string& name) {
        metadata_.name = name;
        return *this;
    }
    
    SegmentBuilder& setDescription(const std::string& description) {
        metadata_.description = description;
        return *this;
    }
    
    SegmentBuilder& setExecutionMode(SegmentExecutionMode mode) {
        metadata_.execution_mode = mode;
        return *this;
    }
    
    SegmentBuilder& setConnectionType(SegmentConnectionType type) {
        metadata_.connection_type = type;
        return *this;
    }
    
    SegmentBuilder& setPriority(double priority) {
        metadata_.priority = priority;
        return *this;
    }
    
    SegmentBuilder& addDependency(size_t segment_id) {
        metadata_.dependencies.push_back(segment_id);
        return *this;
    }
    
    SegmentBuilder& setParameter(const std::string& key, double value) {
        metadata_.parameters[key] = value;
        return *this;
    }
    
    size_t build(TrajectorySegmentManager<DOF>& manager) {
        return manager.addSegment(waypoints_, metadata_);
    }
    
    void clear() {
        waypoints_.clear();
        metadata_ = SegmentMetadata<DOF>();
    }
};

#endif // TRAJECTORY_SEGMENT_MANAGER_HPP
