#ifndef RUCKIG_INTERPOLATOR_H
#define RUCKIG_INTERPOLATOR_H

#include "TrajInterpolatorBase.hpp"
#include <iostream>
#include <cmath>
#include <memory>
#include <ruckig/ruckig.hpp>

template <int DOF>
class RuckigInterpolator : public TrajectoryInterpolator<DOF> {
public:
    using VectorDOF = typename TrajectoryInterpolator<DOF>::VectorDOF;
    using MatrixDOF = typename TrajectoryInterpolator<DOF>::MatrixDOF;

    RuckigInterpolator(double dt = 0.001, const std::string& urdf_path = "")
        : TrajectoryInterpolator<DOF>(dt, urdf_path), otg_(dt)
    {
        input_.minimum_duration = dt;
    }

    // 必须实现的纯虚函数
    bool computeOffline(const std::vector<MotionState<DOF>>& waypoints) override {
        if (waypoints.size() < 2) {
            this->setError("At least 2 waypoints are required for offline planning");
            return false;
        }

        this->clearError();
        std::vector<TrajectoryState<DOF>> all_states;
        std::vector<double> all_timestamps;
        double current_time = 0.0;

        // 计算每个路径段
        for (size_t i = 0; i < waypoints.size() - 1; ++i) {
            std::vector<TrajectoryState<DOF>> segment_states;
            std::vector<double> segment_timestamps;
            
            if (!computeTrajectorySegment(waypoints[i], waypoints[i + 1], current_time, 
                                        segment_states, segment_timestamps)) {
                this->setError("Failed to compute segment " + std::to_string(i));
                return false;
            }

            // 添加段数据到总轨迹（跳过第一个点以避免重复，除了第一段）
            mergeTrajectorySegments(all_states, all_timestamps, 
                                  segment_states, segment_timestamps, i == 0);
        }

        // 设置轨迹缓冲区
        this->trajectory_buffer_.setTrajectory(all_states, all_timestamps);
        return true;
    }

    bool computeOnline(const MotionState<DOF>& current_state,
                       const MotionState<DOF>& target_state) override {
        this->clearError();
        
        // 计算单段轨迹
        std::vector<TrajectoryState<DOF>> states;
        std::vector<double> timestamps;
        double start_time = 0.0;
        
        if (!computeTrajectorySegment(current_state, target_state, start_time, 
                                    states, timestamps)) {
            this->setError("Failed to compute online trajectory");
            return false;
        }

        // 设置轨迹缓冲区
        this->trajectory_buffer_.setTrajectory(states, timestamps);
        return true;
    }

    std::string getTypeName() const override {
        return "RuckigInterpolator<" + std::to_string(DOF) + ">";
    }

private:
    ruckig::Ruckig<DOF> otg_;
    ruckig::InputParameter<DOF> input_;
    ruckig::OutputParameter<DOF> output_;
    ruckig::Trajectory<DOF> trajectory_;

    // 设置输入约束（提取的公共方法）
    void setupInputConstraints() {
        const auto& constraints = this->getConstraints();
        for (int i = 0; i < DOF; ++i) {
            input_.max_velocity[i] = constraints.max_velocity[i];
            input_.max_acceleration[i] = constraints.max_acceleration[i];
            input_.max_jerk[i] = constraints.max_jerk[i];
        }
    }

    // 设置运动状态（提取的公共方法）
    void setMotionStates(const MotionState<DOF>& start_state, 
                        const MotionState<DOF>& end_state) {
        for (int i = 0; i < DOF; ++i) {
            // 起始状态
            input_.current_position[i] = start_state.position[i];
            input_.current_velocity[i] = start_state.velocity[i];
            input_.current_acceleration[i] = start_state.acceleration[i];
            
            // 目标状态
            input_.target_position[i] = end_state.position[i];
            input_.target_velocity[i] = end_state.velocity[i];
            input_.target_acceleration[i] = end_state.acceleration[i];
        }
    }

    // 从输出参数创建轨迹状态（提取的公共方法）
    TrajectoryState<DOF> createTrajectoryState(double timestamp) const {
        TrajectoryState<DOF> state;
        for (int i = 0; i < DOF; ++i) {
            state.position[i] = output_.new_position[i];
            state.velocity[i] = output_.new_velocity[i];
            state.acceleration[i] = output_.new_acceleration[i];
        }
        state.timestamp = timestamp;
        state.valid = true;
        return state;
    }

    // 采样轨迹到状态列表（核心重构方法）
    bool sampleTrajectory(double start_time, 
                         std::vector<TrajectoryState<DOF>>& states,
                         std::vector<double>& timestamps) {
        double duration = trajectory_.get_duration();
        int num_samples = static_cast<int>(std::ceil(duration / this->dt_)) + 1;
        
        states.clear();
        timestamps.clear();
        states.reserve(num_samples);
        timestamps.reserve(num_samples);

        for (int i = 0; i < num_samples; ++i) {
            double t = std::min(i * this->dt_, duration);
            double absolute_time = start_time + t;
            
            // 获取轨迹在时间t的状态
            trajectory_.at_time(t, output_.new_position, 
                              output_.new_velocity, output_.new_acceleration);
            
            // 创建轨迹状态
            TrajectoryState<DOF> state = createTrajectoryState(absolute_time);
            
            states.push_back(state);
            timestamps.push_back(absolute_time);
        }

        return true;
    }

    // 计算单个轨迹段（重构的核心方法）
    bool computeTrajectorySegment(const MotionState<DOF>& start_state,
                                 const MotionState<DOF>& end_state,
                                 double& current_time,
                                 std::vector<TrajectoryState<DOF>>& states,
                                 std::vector<double>& timestamps) {
        // 设置约束和状态
        setupInputConstraints();
        setMotionStates(start_state, end_state);

        // 计算轨迹
        auto result = otg_.calculate(input_, trajectory_);
        if (result != ruckig::Result::Working && result != ruckig::Result::Finished) {
            return false;
        }

        // 采样轨迹
        if (!sampleTrajectory(current_time, states, timestamps)) {
            return false;
        }

        // 更新当前时间
        current_time += trajectory_.get_duration();
        return true;
    }

    // 合并轨迹段（处理重复点）
    void mergeTrajectorySegments(std::vector<TrajectoryState<DOF>>& all_states,
                               std::vector<double>& all_timestamps,
                               const std::vector<TrajectoryState<DOF>>& segment_states,
                               const std::vector<double>& segment_timestamps,
                               bool is_first_segment) {
        // 跳过第一个点以避免重复（除了第一段）
        size_t start_idx = is_first_segment ? 0 : 1;
        
        for (size_t i = start_idx; i < segment_states.size(); ++i) {
            all_states.push_back(segment_states[i]);
            all_timestamps.push_back(segment_timestamps[i]);
        }
    }
};

#endif // RUCKIG_INTERPOLATOR_H