#ifndef COMM_FACTORY_H
#define COMM_FACTORY_H

#include "CommBase.h"
#include "ZMQComm.h"
#include <memory>

class CommFactory {
public:
    static std::unique_ptr<CommBase> createComm(CommType type, bool is_server = false) {
        switch (type) {
            case CommType::ZMQ:
                return std::make_unique<ZMQComm>(is_server);
            default:
                return nullptr;
        }
    }
};

#endif // COMM_FACTORY_H 