#ifndef ROB_COMM_H
#define ROB_COMM_H

#include <atomic>
#include "CommBase.h"
#include <memory>
#include <thread>
#include <queue>
#include <mutex>
#include <functional>

// 功能指令枚举
enum class RobotCommand {
    SYS_INIT,
    GET_STATE,
    RESET,
    QUERY_ORIGIN,
    HOMING,
    START_SERVO,
    STOP_SERVO,
    UNKNOWN
};

class RobComm {
public:
    explicit RobComm(CommType type = CommType::ZMQ);
    ~RobComm();
    
    // 基础连接管理

    bool init();
    void close();
    bool isConnected() const;
    
    // Request-Reply 模式：机器人通信层向硬件层请求执行功能指令
    std::string requestReply(const std::string& cmd);
    
    // 实时数据发送：机器人通信层发送给硬件板卡层，没有返回
    bool sendRtData(const ServoCommand& cmd);
    
    // 实时数据接收：硬件板卡层发送给机器人通信层，回调函数形式
    bool recvRtData(const std::function<void(const RobotState&)>& callback);
    
    // 反馈通道：硬件到机器人
    bool sendFeedback(const std::string& feedback);
    bool recvFeedback(const std::function<void(const std::string&)>& callback);
    
    // 业务接口
    int init_robot();
    int get_state();
    int reset();
    int query_origin();
    int homing();
    int start_servo(int mode, double gain, double smooth, int period);
    int stop_servo();
    
    // 工具方法
    void switchCommType(CommType type);
    std::string get_connection_info() const;
    RobotState get_latest_state() const;

private:
    CommType current_type_;
    std::unique_ptr<CommBase> comm_;
    std::thread recv_thread_;
    std::thread send_thread_;
    std::atomic<bool> stop_flag_;
    
    // 状态管理
    mutable std::mutex state_mtx_;
    RobotState latest_state_;
    
    // 命令队列
    std::mutex queue_mtx_;
    std::queue<ServoCommand> cmd_queue_;
    
    // 回调函数
    std::function<void(const RobotState&)> rt_data_callback_;
    
    // 内部方法
    void recv_loop();
    void send_loop();
    uint64_t get_timestamp_us() const;

    bool is_connected_ = false;
};

#endif // ROB_COMM_H 