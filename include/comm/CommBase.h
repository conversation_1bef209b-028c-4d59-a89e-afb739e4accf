#ifndef COMM_BASE_H
#define COMM_BASE_H

#include <cstdint>
#include <string>
#include <memory>
#include <functional>

// 机器人状态结构体
#pragma pack(push, 1)
struct RobotState {
    uint64_t timestamp_us;
    double   position[6] = {0};
    double   velocity[6] = {0};
    double   current[6] = {0};
    uint64_t servo_status;
};
//伺服指令结构体
struct ServoCommand {
    uint64_t timestamp_us;
    double   position[6]= {0};
    double   velocity[6]= {0};
    uint64_t duration_ms;
};
#pragma pack(pop)

// 通信类型枚举
enum class CommType {
    ZMQ,
    FIFO
};

// 抽象通信接口
class CommBase {
public:
    virtual ~CommBase() = default;
    
    // 基础连接管理
    virtual bool init() = 0;
    virtual void close() = 0;
    virtual bool isConnected() const = 0;

    // Request-Reply 模式：机器人通信层向硬件层请求执行功能指令
    virtual std::string requestReply(const std::string& cmd) = 0;

    // 实时数据发送：机器人通信层发送给硬件板卡层，没有返回
    virtual bool sendRtData(const ServoCommand& cmd) = 0;
    
    // 实时数据发送：硬件层发送给机器人通信层，没有返回
    virtual bool sendRtData(const RobotState& state) = 0;

    // 实时数据接收：硬件板卡层发送给机器人通信层，回调函数形式
    virtual bool recvRtData(const std::function<void(const ServoCommand&)>& callback) = 0;
    
    // 实时数据接收：机器人通信层接收硬件板卡层状态，回调函数形式
    virtual bool recvRtData(const std::function<void(const RobotState&)>& callback) = 0;

    // 中断接收指令，返回结果：硬件板卡接收功能指令，根据枚举执行，返回结果
    virtual std::string recvReply() = 0;

    // 反馈通道：硬件->机器人通信层
    virtual bool sendFeedbackData(const std::string& data) = 0;
    virtual bool recvFeedbackData(const std::function<void(const std::string&)>& callback) = 0;

    // 工具方法
    virtual uint64_t getCurrentTimestamp() const = 0;
    virtual std::string getConnectionInfo() const = 0;
};

#endif // COMM_BASE_H 