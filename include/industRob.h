#pragma once
#include "comm/RobComm.h"
#include "RobotModel.h"
#include <string>
#include <functional>
#include <vector>
#include <memory>

// 统一机器人状态结构体，包含关节与笛卡尔信息
struct RobotFullState {
    uint64_t timestamp_us;
    std::vector<double> joint_position;   // 6
    std::vector<double> joint_velocity;   // 6
    uint32_t servo_status;
    std::vector<double> cartesian_position; // 6
    std::vector<double> cartesian_velocity; // 6
};

class industRob {
public:
    explicit industRob(CommType type = CommType::ZMQ);
    explicit industRob(CommType type, const std::string& urdf_path, const std::string& end_effector_frame = "");
    ~industRob();

    // 连接管理
    void connect();
    void disconnect();
    bool isConnected();

    // 机器人功能接口
    void init();
    void init(const std::string& urdf_path, const std::string& end_effector_frame = "");
    void reset();

    // 参数设置
    void setMotionPara(double vec, double acc);
    void servoMode(int mode, int controlPeriod = 10, double smoothRatio = 1, double responseRatio = 0.1);

    // 运动学
    std::vector<double> forwardKinematics(const std::vector<double>& jointRad);
    bool inverseKinematics(std::vector<double>& jointRad, const std::vector<double>& pose);

    // 运动指令 (带统一限位检查)
    void movAbsJ(const std::vector<double>& jointRad, double acc, double vec, double blendRadius);
    void movJ(const std::vector<double>& xyzabc, double acc, double vec, double blendRadius);
    void movL(const std::vector<double>& xyzabc, double acc, double vec, double blendRadius);
    void movL(const std::vector<std::vector<double>>& path, double acc, double vec, double blendRadius);
    void waitRobFree();

    // 伺服/速度控制
    void servoj(const std::vector<double>& jointRad, double dt, double lookahead_time, double gain);
    void servoL(const std::vector<double>& xyzabc, double dt, double lookahead_time, double gain);
    void speedj(const std::vector<double>& jointRad, double acc, double dt);
    void speedL(const std::vector<double>& xyzabc, double acc, double dt);

    // IO
    void setIO(int index, int val);
    int getIO(int index);

    // 信息获取
    std::vector<double> getJoint();
    std::vector<double> getTcpPose();
    std::vector<double> getJointSpeed();
    std::vector<double> getTcpSpeed();

    // 时间相关
    double initPerio();
    void waitPeriod(double startTime, double TimeCorrection = 0);

    // 统一状态回调注册
    void registerStateCallback(const std::function<void(const RobotFullState&)>& cb);

    // 获取连接信息 (用于示例)
    std::string getConnectionInfo() const;

private:
    class impl;
    std::unique_ptr<impl> pimpl_;
}; 