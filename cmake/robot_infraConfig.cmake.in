@PACKAGE_INIT@

# robot_infra CMake configuration file

include(CMakeFindDependencyMacro)

# 查找依赖项
find_dependency(Eigen3 REQUIRED)
find_dependency(PkgConfig REQUIRED)

# 查找ZMQ
if(NOT TARGET PkgConfig::ZMQ)
    pkg_check_modules(ZMQ REQUIRED IMPORTED_TARGET libzmq)
endif()

# 可选依赖项
find_package(ruckig QUIET)
find_package(toppra QUIET)

# 包含目标文件
include("${CMAKE_CURRENT_LIST_DIR}/robot_infraTargets.cmake")

# 设置变量
set(robot_infra_FOUND TRUE)
set(robot_infra_VERSION @robot_infra_VERSION@)

# 检查组件
check_required_components(robot_infra)

# 提供便利变量
set(robot_infra_LIBRARIES robot_infra::robot_infra)
set(robot_infra_INCLUDE_DIRS "${PACKAGE_PREFIX_DIR}/include/robot_infra")

# 打印找到的信息
if(NOT robot_infra_FIND_QUIETLY)
    message(STATUS "Found robot_infra: ${robot_infra_VERSION}")
    message(STATUS "robot_infra libraries: ${robot_infra_LIBRARIES}")
    message(STATUS "robot_infra include dirs: ${robot_infra_INCLUDE_DIRS}")
endif()
