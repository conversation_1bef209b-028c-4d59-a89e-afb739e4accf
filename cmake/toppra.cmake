# toppra.cmake - locate toppra library via CMake config mode
# Check local third_party build directory for installed config
set(_TPPRA_LOCAL_BUILD_DIR "${CMAKE_SOURCE_DIR}/third_party/toppra/cpp/build")
if(EXISTS "${_TPPRA_LOCAL_BUILD_DIR}/toppraConfig.cmake" OR EXISTS "${_TPPRA_LOCAL_BUILD_DIR}/toppra-config.cmake")
  set(toppra_DIR "${_TPPRA_LOCAL_BUILD_DIR}")
endif()

# Add toppra source include directory (for headers not yet installed)
set(_TPPRA_SRC_DIR "${CMAKE_SOURCE_DIR}/third_party/toppra/cpp/src")
if(EXISTS "${_TPPRA_SRC_DIR}/toppra/constraint")
  include_directories("${_TPPRA_SRC_DIR}")
endif()

# Use find_package in CONFIG mode to get toppra::toppra target
find_package(toppra 0.6.2 CONFIG REQUIRED
  PATHS
    ${_TPPRA_LOCAL_BUILD_DIR}
    $ENV{CMAKE_PREFIX_PATH}
    /usr/local
  NO_DEFAULT_PATH
)
