#!/bin/bash

# robot_infra 库构建和安装脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认参数
BUILD_TYPE="Release"
INSTALL_PREFIX="/usr/local"
BUILD_TESTS="ON"
BUILD_EXAMPLES="ON"
CLEAN_BUILD="OFF"
PACKAGE="OFF"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --prefix)
            INSTALL_PREFIX="$2"
            shift 2
            ;;
        --no-tests)
            BUILD_TESTS="OFF"
            shift
            ;;
        --no-examples)
            BUILD_EXAMPLES="OFF"
            shift
            ;;
        --clean)
            CLEAN_BUILD="ON"
            shift
            ;;
        --package)
            PACKAGE="ON"
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --debug          构建Debug版本 (默认: Release)"
            echo "  --prefix PATH    安装路径 (默认: /usr/local)"
            echo "  --no-tests       不构建测试"
            echo "  --no-examples    不构建示例"
            echo "  --clean          清理构建目录"
            echo "  --package        创建安装包"
            echo "  --help, -h       显示此帮助信息"
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            exit 1
            ;;
    esac
done

print_info "robot_infra 库构建脚本"
print_info "构建类型: $BUILD_TYPE"
print_info "安装路径: $INSTALL_PREFIX"

# 检查依赖
print_info "检查依赖项..."

# 检查CMake
if ! command -v cmake &> /dev/null; then
    print_error "CMake 未安装"
    exit 1
fi

# 检查必要的库
check_lib() {
    if pkg-config --exists $1; then
        print_success "找到 $1"
    else
        print_warning "$1 未找到，可能需要手动安装"
    fi
}

check_lib "eigen3"
check_lib "libzmq"

# 清理构建目录
if [[ "$CLEAN_BUILD" == "ON" ]] && [[ -d "build" ]]; then
    print_info "清理构建目录..."
    rm -rf build
fi

# 创建构建目录
print_info "创建构建目录..."
mkdir -p build
cd build

# 配置项目
print_info "配置项目..."
cmake .. \
    -DCMAKE_BUILD_TYPE=$BUILD_TYPE \
    -DCMAKE_INSTALL_PREFIX=$INSTALL_PREFIX \
    -DBUILD_TESTING=$BUILD_TESTS \
    -DBUILD_EXAMPLES=$BUILD_EXAMPLES

# 构建项目
print_info "构建项目..."
make -j$(nproc)

# 运行测试
if [[ "$BUILD_TESTS" == "ON" ]]; then
    print_info "运行测试..."
    if make test; then
        print_success "所有测试通过"
    else
        print_warning "部分测试失败"
    fi
fi

# 安装
print_info "安装库..."
if [[ "$INSTALL_PREFIX" == "/usr/local" ]] || [[ "$INSTALL_PREFIX" == "/usr" ]]; then
    sudo make install
else
    make install
fi

# 更新动态库缓存
if [[ "$INSTALL_PREFIX" == "/usr/local" ]] || [[ "$INSTALL_PREFIX" == "/usr" ]]; then
    print_info "更新动态库缓存..."
    sudo ldconfig
fi

# 创建包
if [[ "$PACKAGE" == "ON" ]]; then
    print_info "创建安装包..."
    make package
    print_success "安装包已创建在 build/ 目录中"
fi

print_success "构建和安装完成!"

# 显示安装信息
print_info "安装信息:"
echo "  库文件: $INSTALL_PREFIX/lib/librobot_infra.so"
echo "  头文件: $INSTALL_PREFIX/include/robot_infra/"
echo "  CMake配置: $INSTALL_PREFIX/lib/cmake/robot_infra/"
echo "  pkg-config: $INSTALL_PREFIX/lib/pkgconfig/robot_infra.pc"

print_info "使用方法:"
echo "  CMake: find_package(robot_infra REQUIRED)"
echo "  pkg-config: pkg-config --cflags --libs robot_infra"

# 验证安装
print_info "验证安装..."
if [[ -f "$INSTALL_PREFIX/lib/librobot_infra.so" ]]; then
    print_success "动态库安装成功"
else
    print_error "动态库安装失败"
fi

if [[ -d "$INSTALL_PREFIX/include/robot_infra" ]]; then
    print_success "头文件安装成功"
else
    print_error "头文件安装失败"
fi
