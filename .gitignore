# IDE and editor folders
.vscode/
.cache/
.idea/

# Dependency artifacts
_deps/

# CMake build directories
build/
cmake-build*/

# CMake generated files
**/CMakeFiles/
**/CMakeCache.txt
**/cmake_install.cmake
**/Makefile
**/Makefile.cmake
**/Makefile2
**/progress.marks
**/TargetDirectories.txt
**/CMakeOutput.log
**/cmake.check_cache
**/cmake.verify_globs
**/VerifyGlobs.cmake
**/CMakeDirectoryInformation.cmake

# Build output folders
**/build/

# Object and library files
*.o
*.obj
*.so
*.so.*
*.a
*.dylib
*.dll
*.lib

# Executables
*.out
*.exe
*.elf

# Library packaging and release files
release/
dist/
packages/
*.tar.gz
*.tar.bz2
*.tar.xz
*.zip
*.deb
*.rpm
*.dmg
*.msi
*.sha256
*.md5

# CMake package files
*Config.cmake
*ConfigVersion.cmake
*Targets.cmake
*.pc

# Installation directories
install/
prefix/

# Python caches and virtual environments
__pycache__/
*.py[cod]
*.pyo
.pytest_cache/
.venv/

# OS files
.DS_Store
Thumbs.db

# Backup and temporary files
*~
*.bak
*.swp
*.swo
*.tmp
*.temp

# Log files
*.log
*.out.txt
*.err.txt

# Test and benchmark results
test_results/
benchmark_results/
coverage/
*.gcov
*.gcda
*.gcno

# Documentation build
docs/_build/
docs/html/
docs/latex/
docs/xml/

# Development tools
.clangd/
.ccls-cache/
compile_commands.json
.clang-format
.clang-tidy

# Runtime files
core
core.*
*.pid
*.lock

# Configuration files (keep templates)
config.ini
config.yaml
config.json
!config.*.template
!config.*.example

# Data and media files
data/
datasets/
*.csv
*.json.bak
*.xml.bak

# Jupyter notebooks checkpoints
.ipynb_checkpoints/
