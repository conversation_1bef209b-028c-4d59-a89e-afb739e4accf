#include "trajectory/TrajectorySegmentManager.hpp"
#include <iostream>
#include <algorithm>
#include <set>

template<int DOF>
size_t TrajectorySegmentManager<DOF>::addSegment(
    const std::vector<MotionState<DOF>>& waypoints,
    const SegmentMetadata<DOF>& metadata) {
    
    if (waypoints.empty()) {
        notifyError("Cannot add segment with empty waypoints");
        return 0;
    }

    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    size_t segment_id = next_id_++;
    
    // 创建轨迹段
    auto segment = std::make_shared<TrajectorySegment<DOF>>(waypoints, segment_id);
    segments_[segment_id] = segment;
    
    // 设置元数据
    SegmentMetadata<DOF> meta = metadata;
    meta.segment_id = segment_id;
    if (meta.name.empty()) {
        meta.name = "Segment_" + std::to_string(segment_id);
    }
    metadata_[segment_id] = meta;
    
    // 添加到执行顺序
    execution_order_.push_back(segment_id);
    
    std::cout << "Added segment " << segment_id << " (" << meta.name 
              << ") with " << waypoints.size() << " waypoints" << std::endl;
    
    // 如果启用自动执行且当前没有运行段，则开始执行
    if (auto_execute_ && current_segment_id_.load() == 0) {
        std::cout << "自动执行段 " << segment_id << std::endl;
        executeSegment(segment_id);
    }
    
    return segment_id;
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::removeSegment(size_t segment_id) {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    auto it = segments_.find(segment_id);
    if (it == segments_.end()) {
        notifyError("Segment " + std::to_string(segment_id) + " not found");
        return false;
    }
    
    // 检查是否为当前执行段
    if (current_segment_id_.load() == segment_id) {
        notifyError("Cannot remove currently executing segment");
        return false;
    }
    
    // 移除段和元数据
    segments_.erase(it);
    metadata_.erase(segment_id);
    
    // 从执行顺序中移除
    auto order_it = std::find(execution_order_.begin(), execution_order_.end(), segment_id);
    if (order_it != execution_order_.end()) {
        execution_order_.erase(order_it);
    }
    
    std::cout << "Removed segment " << segment_id << std::endl;
    return true;
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::executeSegment(size_t segment_id, bool immediate) {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    auto it = segments_.find(segment_id);
    if (it == segments_.end()) {
        notifyError("Segment " + std::to_string(segment_id) + " not found");
        return false;
    }
    // 检查依赖关系
    if (!validateSegmentDependencies(segment_id)) {
        notifyError("Segment dependencies not satisfied");
        return false;
    }
    
    // 添加到规划器
    bool success = hybrid_planner_->addTrajectorySegment(it->second->waypoints, immediate);
    if (success) {
        current_segment_id_.store(segment_id);
        it->second->status = SegmentStatus::ACTIVE;
        notifySegmentStatus(segment_id, SegmentStatus::ACTIVE);
        
        std::cout << "Executing segment " << segment_id 
                  << " (" << metadata_[segment_id].name << ")" << std::endl;
    }
    
    return success;
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::executeSequence(const std::vector<size_t>& segment_ids) {
    if (segment_ids.empty()) {
        return true;
    }
    
    // 解析依赖关系
    auto resolved_ids = resolveDependencies(segment_ids);
    
    // 根据执行模式选择策略
    auto& first_metadata = metadata_[resolved_ids[0]];
    switch (first_metadata.execution_mode) {
        case SegmentExecutionMode::SEQUENTIAL:
            return executeSequential(resolved_ids);
        case SegmentExecutionMode::PARALLEL:
            return executeParallel(resolved_ids);
        case SegmentExecutionMode::CONDITIONAL:
            return executeConditional(resolved_ids);
        default:
            return executeSequential(resolved_ids);
    }
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::switchToSegment(size_t segment_id, double transition_duration) {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    auto it = segments_.find(segment_id);
    if (it == segments_.end()) {
        notifyError("Segment " + std::to_string(segment_id) + " not found");
        return false;
    }
    
    double duration = (transition_duration < 0) ? default_transition_duration_ : transition_duration;
    
    // 请求轨迹切换
    bool success = hybrid_planner_->requestTrajectoryTransition(it->second->waypoints, duration);
    if (success) {
        // 取消当前段
        if (current_segment_id_.load() != 0) {
            auto current_it = segments_.find(current_segment_id_.load());
            if (current_it != segments_.end()) {
                current_it->second->status = SegmentStatus::CANCELLED;
                notifySegmentStatus(current_segment_id_.load(), SegmentStatus::CANCELLED);
            }
        }
        
        current_segment_id_.store(segment_id);
        it->second->status = SegmentStatus::ACTIVE;
        notifySegmentStatus(segment_id, SegmentStatus::ACTIVE);
        
        std::cout << "Switching to segment " << segment_id 
                  << " with " << duration << "s transition" << std::endl;
    }
    
    return success;
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::start() {
    if (is_running_.load()) {
        return true;
    }
    
    if (!hybrid_planner_->start()) {
        notifyError("Failed to start hybrid planner");
        return false;
    }
    
    is_running_.store(true);
    std::cout << "TrajectorySegmentManager started" << std::endl;
    return true;
}

template<int DOF>
void TrajectorySegmentManager<DOF>::stop() {
    is_running_.store(false);
    hybrid_planner_->stop();
    
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    // 取消所有活动段
    for (auto& [id, segment] : segments_) {
        if (segment->status == SegmentStatus::ACTIVE) {
            segment->status = SegmentStatus::CANCELLED;
            notifySegmentStatus(id, SegmentStatus::CANCELLED);
        }
    }
    
    current_segment_id_.store(0);
    std::cout << "TrajectorySegmentManager stopped" << std::endl;
}

template<int DOF>
typename TrajectorySegmentManager<DOF>::SegmentPtr
TrajectorySegmentManager<DOF>::getSegment(size_t segment_id) const {
    std::lock_guard<std::mutex> lock(segments_mutex_);

    auto it = segments_.find(segment_id);
    return (it != segments_.end()) ? it->second : nullptr;
}

template<int DOF>
typename TrajectorySegmentManager<DOF>::SegmentPtr
TrajectorySegmentManager<DOF>::getCurrentSegment() const {
    std::lock_guard<std::mutex> lock(segments_mutex_);

    size_t current_id = current_segment_id_.load();
    if (current_id == 0) {
        return nullptr;
    }

    auto it = segments_.find(current_id);
    return (it != segments_.end()) ? it->second : nullptr;
}

template<int DOF>
size_t TrajectorySegmentManager<DOF>::getQueueSize() const {
    return hybrid_planner_->getQueueSize();
}

template<int DOF>
SegmentMetadata<DOF> TrajectorySegmentManager<DOF>::getMetadata(size_t segment_id) const {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    auto it = metadata_.find(segment_id);
    return (it != metadata_.end()) ? it->second : SegmentMetadata<DOF>();
}

template<int DOF>
std::vector<size_t> TrajectorySegmentManager<DOF>::getAllSegmentIds() const {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    std::vector<size_t> ids;
    ids.reserve(segments_.size());
    for (const auto& [id, segment] : segments_) {
        ids.push_back(id);
    }
    return ids;
}

template<int DOF>
std::vector<size_t> TrajectorySegmentManager<DOF>::getPendingSegments() const {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    std::vector<size_t> pending;
    for (const auto& [id, segment] : segments_) {
        if (segment->status == SegmentStatus::PENDING) {
            pending.push_back(id);
        }
    }
    return pending;
}

template<int DOF>
void TrajectorySegmentManager<DOF>::clear() {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    segments_.clear();
    metadata_.clear();
    execution_order_.clear();
    current_segment_id_.store(0);
    next_id_.store(1);
    
    hybrid_planner_->clearQueue();
    std::cout << "Cleared all segments" << std::endl;
}

template<int DOF>
size_t TrajectorySegmentManager<DOF>::getTotalSegments() const {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    return segments_.size();
}

template<int DOF>
size_t TrajectorySegmentManager<DOF>::getCompletedSegments() const {
    std::lock_guard<std::mutex> lock(segments_mutex_);
    
    size_t count = 0;
    for (const auto& [id, segment] : segments_) {
        if (segment->status == SegmentStatus::COMPLETED) {
            count++;
        }
    }
    return count;
}

// 私有方法实现
template<int DOF>
void TrajectorySegmentManager<DOF>::onSegmentCompleted(size_t segment_id) {
    std::lock_guard<std::mutex> lock(segments_mutex_);

    auto it = segments_.find(segment_id);
    if (it != segments_.end()) {
        it->second->status = SegmentStatus::COMPLETED;
        notifySegmentStatus(segment_id, SegmentStatus::COMPLETED);

        std::cout << "Segment " << segment_id << " completed" << std::endl;

        // 如果启用自动执行，尝试执行下一个段
        if (auto_execute_) {
            auto next_it = std::find(execution_order_.begin(), execution_order_.end(), segment_id);
            if (next_it != execution_order_.end() && ++next_it != execution_order_.end()) {
                executeSegment(*next_it);
            }
        }
    }

    current_segment_id_.store(0);
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::validateSegmentDependencies(size_t segment_id) const {
    auto meta_it = metadata_.find(segment_id);
    if (meta_it == metadata_.end()) {
        return false;
    }

    // 检查所有依赖段是否已完成
    for (size_t dep_id : meta_it->second.dependencies) {
        auto dep_it = segments_.find(dep_id);
        if (dep_it == segments_.end() || dep_it->second->status != SegmentStatus::COMPLETED) {
            return false;
        }
    }

    return true;
}

template<int DOF>
std::vector<size_t> TrajectorySegmentManager<DOF>::resolveDependencies(
    const std::vector<size_t>& segment_ids) const {

    std::vector<size_t> resolved;
    std::set<size_t> visited;

    std::function<void(size_t)> visit = [&](size_t id) {
        if (visited.count(id)) return;
        visited.insert(id);

        auto meta_it = metadata_.find(id);
        if (meta_it != metadata_.end()) {
            // 先访问依赖
            for (size_t dep_id : meta_it->second.dependencies) {
                visit(dep_id);
            }
        }

        resolved.push_back(id);
    };

    for (size_t id : segment_ids) {
        visit(id);
    }

    return resolved;
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::executeSequential(const std::vector<size_t>& segment_ids) {
    for (size_t id : segment_ids) {
        if (!executeSegment(id)) {
            return false;
        }

        // 等待当前段完成（在实际应用中可能需要异步处理）
        // 这里简化处理，实际应该通过回调机制处理
    }
    return true;
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::executeParallel(const std::vector<size_t>& segment_ids) {
    // 并行执行（适用于多机器人系统）
    // 这里简化为顺序执行，实际实现需要多线程支持
    notifyError("Parallel execution not yet implemented");
    return false;
}

template<int DOF>
bool TrajectorySegmentManager<DOF>::executeConditional(const std::vector<size_t>& segment_ids) {
    // 条件执行（基于外部条件）
    // 这里简化为顺序执行，实际实现需要条件检查机制
    notifyError("Conditional execution not yet implemented");
    return false;
}

template<int DOF>
void TrajectorySegmentManager<DOF>::notifySegmentStatus(size_t segment_id, SegmentStatus status) {
    if (segment_callback_) {
        segment_callback_(segment_id, status);
    }
}

template<int DOF>
void TrajectorySegmentManager<DOF>::notifyError(const std::string& error) {
    std::cerr << "TrajectorySegmentManager Error: " << error << std::endl;
    if (error_callback_) {
        error_callback_(error);
    }
}

// 显式实例化
template class TrajectorySegmentManager<6>;
template class TrajectorySegmentManager<7>;
