#include "trajectory/HybridTrajectoryManager.hpp"
#include <iostream>
#include <chrono>
#include <sstream>

template<int DOF>
bool HybridTrajectoryManager<DOF>::initialize() {
    try {
        // 初始化组件
        if (!hybrid_planner_->start()) {
            std::cerr << "Failed to start hybrid planner" << std::endl;
            return false;
        }
        
        if (!segment_manager_->start()) {
            std::cerr << "Failed to start segment manager" << std::endl;
            return false;
        }
        
        // 重置时间
        resetTime();
        
        std::cout << "HybridTrajectoryManager initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize HybridTrajectoryManager: " << e.what() << std::endl;
        return false;
    }
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::start() {
    if (is_running_.load()) {
        return true;
    }
    
    if (!initialize()) {
        return false;
    }
    
    // 启动控制线程
    control_thread_running_.store(true);
    control_thread_ = std::thread(&HybridTrajectoryManager<DOF>::controlLoop, this);
    
    is_running_.store(true);
    current_mode_.store(HybridTrajectoryMode::IDLE);
    
    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::MODE_CHANGED, "Manager started");
    event.to_mode = HybridTrajectoryMode::IDLE;
    notifyEvent(event);
    
    std::cout << "HybridTrajectoryManager started at " << config_.control_frequency 
              << " Hz" << std::endl;
    return true;
}

template<int DOF>
void HybridTrajectoryManager<DOF>::stop() {
    if (!is_running_.load()) {
        return;
    }
    
    is_running_.store(false);
    control_thread_running_.store(false);
    
    // 等待控制线程结束
    if (control_thread_.joinable()) {
        control_thread_.join();
    }
    
    // 停止组件
    hybrid_planner_->stop();
    segment_manager_->stop();
    
    current_mode_.store(HybridTrajectoryMode::IDLE);
    
    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::MODE_CHANGED, "Manager stopped");
    event.from_mode = current_mode_.load();
    event.to_mode = HybridTrajectoryMode::IDLE;
    notifyEvent(event);
    
    std::cout << "HybridTrajectoryManager stopped" << std::endl;
}

template<int DOF>
MotionState<DOF> HybridTrajectoryManager<DOF>::getCurrentState() const {
    std::lock_guard<std::mutex> lock(state_mutex_);
    return current_state_;
}

template<int DOF>
size_t HybridTrajectoryManager<DOF>::addTrajectorySegment(
    const std::vector<MotionState<DOF>>& waypoints,
    const SegmentMetadata<DOF>& metadata) {
    
    if (!validateState(waypoints[0])) {
        onError("Invalid waypoint in trajectory segment");
        return 0;
    }
    
    size_t segment_id = segment_manager_->addSegment(waypoints, metadata);
    
    if (segment_id > 0) {
        HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::TRAJECTORY_UPDATED, 
                                     "New segment added");
        event.segment_id = segment_id;
        notifyEvent(event);
    }
    
    return segment_id;
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::switchToHybridMode(
    const std::vector<MotionState<DOF>>& waypoints) {

    if (waypoints.empty()) {
        onError("Empty waypoints for hybrid mode");
        return false;
    }

    // 直接使用HybridTrajectoryPlanner执行轨迹，而不是通过SegmentManager
    bool success = hybrid_planner_->addTrajectorySegment(waypoints, true); // immediate=true
    if (!success) {
        onError("Failed to add trajectory to hybrid planner");
        return false;
    }

    // 切换到混合模式
    bool mode_switch = switchToMode(HybridTrajectoryMode::HYBRID);

    if (mode_switch) {
        std::cout << "Successfully switched to hybrid mode with "
                  << waypoints.size() << " waypoints" << std::endl;
    }

    return mode_switch;
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::switchToMode(HybridTrajectoryMode mode, 
                                               double transition_duration) {
    
    HybridTrajectoryMode current = current_mode_.load();
    
    if (current == mode) {
        return true;  // 已经是目标模式
    }
    
    if (!validateModeTransition(current, mode)) {
        onError("Invalid mode transition from " + std::to_string(static_cast<int>(current)) +
                " to " + std::to_string(static_cast<int>(mode)));
        return false;
    }
    
    return performModeTransition(mode, transition_duration);
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::setOnlineTarget(const MotionState<DOF>& target_state) {
    if (!validateState(target_state)) {
        onError("Invalid online target state");
        return false;
    }
    
    // 如果不在在线模式，先切换
    if (current_mode_.load() != HybridTrajectoryMode::ONLINE) {
        if (!switchToMode(HybridTrajectoryMode::ONLINE)) {
            return false;
        }
    }
    
    // 设置在线目标
    std::lock_guard<std::mutex> lock(state_mutex_);
    return hybrid_planner_->requestTrajectoryTransition({target_state}, 
                                                       config_.default_transition_duration);
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::emergencyStop() {
    emergency_stop_.store(true);
    current_mode_.store(HybridTrajectoryMode::EMERGENCY);
    
    // 执行紧急停止
    bool success = hybrid_planner_->emergencyStop();
    
    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::EMERGENCY_TRIGGERED, 
                                 "Emergency stop activated");
    notifyEvent(event);
    
    std::cout << "Emergency stop " << (success ? "activated" : "failed") << std::endl;
    return success;
}

template<int DOF>
void HybridTrajectoryManager<DOF>::updateCurrentState(const MotionState<DOF>& state) {
    {
        std::lock_guard<std::mutex> lock(state_mutex_);
        current_state_ = state;
    }
    
    // 更新规划器状态
    hybrid_planner_->updateCurrentState(state);
    
    // 通知状态更新
    TrajectoryState<DOF> traj_state;
    traj_state.position = state.position;
    traj_state.velocity = state.velocity;
    traj_state.acceleration = state.acceleration;
    traj_state.timestamp = current_time_.load();
    traj_state.valid = true;
    
    notifyStateUpdate(traj_state);
}

template<int DOF>
void HybridTrajectoryManager<DOF>::updateTime(double dt) {
    double new_time = current_time_.load() + dt;
    current_time_.store(new_time);
}

template<int DOF>
void HybridTrajectoryManager<DOF>::controlLoop() {
    auto next_cycle = std::chrono::steady_clock::now();
    auto cycle_duration = std::chrono::microseconds(static_cast<int>(config_.dt * 1000000));
    
    std::cout << "Control loop started with " << config_.dt * 1000 << "ms period" << std::endl;
    
    while (control_thread_running_.load()) {
        auto cycle_start = std::chrono::steady_clock::now();
        
        try {
            // 处理当前模式
            TrajectoryState<DOF> target = processCurrentMode();
            
            // 更新时间
            updateTime(config_.dt);
            
            // 通知状态更新
            if (target.valid) {
                notifyStateUpdate(target);
            }
            
        } catch (const std::exception& e) {
            onError("Control loop error: " + std::string(e.what()));
        }
        
        // 等待下一个周期
        next_cycle += cycle_duration;
        std::this_thread::sleep_until(next_cycle);
    }
    
    std::cout << "Control loop stopped" << std::endl;
}

template<int DOF>
TrajectoryState<DOF> HybridTrajectoryManager<DOF>::processCurrentMode() {
    HybridTrajectoryMode mode = current_mode_.load();
    
    switch (mode) {
        case HybridTrajectoryMode::OFFLINE:
            return processOfflineMode();
            
        case HybridTrajectoryMode::ONLINE:
            return processOnlineMode();
            
        case HybridTrajectoryMode::HYBRID:
            return processHybridMode();
            
        case HybridTrajectoryMode::TRANSITION:
            return processTransitionMode();
            
        case HybridTrajectoryMode::EMERGENCY:
            return processEmergencyMode();
            
        case HybridTrajectoryMode::IDLE:
        default:
            // 返回当前状态
            std::lock_guard<std::mutex> lock(state_mutex_);
            TrajectoryState<DOF> state;
            state.position = current_state_.position;
            state.velocity = current_state_.velocity;
            state.acceleration = current_state_.acceleration;
            state.timestamp = current_time_.load();
            state.valid = true;
            return state;
    }
}

template<int DOF>
TrajectoryState<DOF> HybridTrajectoryManager<DOF>::processHybridMode() {
    // 混合模式：使用混合规划器获取目标
    double current_time = current_time_.load();
    auto target = hybrid_planner_->getCurrentTarget(current_time);

    // 调试输出
    static int debug_count = 0;
    if (++debug_count % 1000 == 0) {  // 每秒打印一次调试信息
        std::cout << "[DEBUG] HybridMode: time=" << current_time
                  << ", target_valid=" << target.valid
                  << ", planner_active=" << hybrid_planner_->isActive()
                  << ", queue_size=" << hybrid_planner_->getQueueSize() << std::endl;
    }

    return target;
}

template<int DOF>
void HybridTrajectoryManager<DOF>::setupCallbacks() {
    // 设置段管理器回调
    segment_manager_->setSegmentCallback(
        [this](size_t segment_id, SegmentStatus status) {
            if (status == SegmentStatus::COMPLETED) {
                onSegmentCompleted(segment_id);
            }
        });
    
    segment_manager_->setErrorCallback(
        [this](const std::string& error) {
            onError("SegmentManager: " + error);
        });
    
    // 设置混合规划器回调
    hybrid_planner_->setCompletionCallback(
        [this](size_t segment_id) {
            onSegmentCompleted(segment_id);
        });
    
    hybrid_planner_->setTransitionCallback(
        [this](const TrajectoryTransitionEvent<DOF>& event) {
            HybridManagerEvent<DOF> mgr_event(HybridManagerEvent<DOF>::TRANSITION_STARTED,
                                             "Trajectory transition started");
            notifyEvent(mgr_event);
        });
}

// 剩余方法实现
template<int DOF>
TrajectoryState<DOF> HybridTrajectoryManager<DOF>::processOfflineMode() {
    // 离线模式：从段管理器获取当前目标
    auto current_segment = segment_manager_->getCurrentSegment();
    if (!current_segment) {
        // 没有活动段，保持当前状态
        std::lock_guard<std::mutex> lock(state_mutex_);
        TrajectoryState<DOF> state;
        state.position = current_state_.position;
        state.velocity = VectorDOF::Zero();
        state.acceleration = VectorDOF::Zero();
        state.timestamp = current_time_.load();
        state.valid = true;
        return state;
    }

    return hybrid_planner_->getCurrentTarget(current_time_.load());
}

template<int DOF>
TrajectoryState<DOF> HybridTrajectoryManager<DOF>::processOnlineMode() {
    // 在线模式：使用混合规划器的在线跟踪功能
    return hybrid_planner_->getCurrentTarget(current_time_.load());
}

template<int DOF>
TrajectoryState<DOF> HybridTrajectoryManager<DOF>::processTransitionMode() {
    // 过渡模式：等待过渡完成
    if (!hybrid_planner_->isTransitioning()) {
        // 过渡完成，切换到目标模式
        onTransitionCompleted();
    }

    return hybrid_planner_->getCurrentTarget(current_time_.load());
}

template<int DOF>
TrajectoryState<DOF> HybridTrajectoryManager<DOF>::processEmergencyMode() {
    // 紧急模式：执行紧急停止轨迹
    auto target = hybrid_planner_->getCurrentTarget(current_time_.load());

    // 检查是否已停止
    bool is_stopped = true;
    for (int i = 0; i < DOF; ++i) {
        if (std::abs(target.velocity[i]) > 1e-3) {
            is_stopped = false;
            break;
        }
    }

    if (is_stopped) {
        std::cout << "Emergency stop completed" << std::endl;
    }

    return target;
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::performModeTransition(HybridTrajectoryMode target_mode,
                                                        double transition_duration) {

    HybridTrajectoryMode current = current_mode_.load();

    // 设置过渡状态
    current_mode_.store(HybridTrajectoryMode::TRANSITION);

    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::TRANSITION_STARTED);
    event.from_mode = current;
    event.to_mode = target_mode;
    notifyEvent(event);

    try {
        // 根据目标模式执行相应的切换逻辑
        bool success = false;

        switch (target_mode) {
            case HybridTrajectoryMode::HYBRID:
                // 切换到混合模式
                success = true;  // 混合规划器已经准备好
                break;

            case HybridTrajectoryMode::ONLINE:
                // 切换到在线模式
                success = true;
                break;

            case HybridTrajectoryMode::OFFLINE:
                // 切换到离线模式
                success = segment_manager_->getQueueSize() > 0;
                break;

            case HybridTrajectoryMode::IDLE:
                // 切换到空闲模式
                success = true;
                break;

            default:
                success = false;
                break;
        }

        if (success) {
            // 等待过渡时间
            if (transition_duration > 0) {
                std::this_thread::sleep_for(
                    std::chrono::milliseconds(static_cast<int>(transition_duration * 1000)));
            }

            current_mode_.store(target_mode);

            HybridManagerEvent<DOF> complete_event(HybridManagerEvent<DOF>::TRANSITION_COMPLETED);
            complete_event.from_mode = current;
            complete_event.to_mode = target_mode;
            notifyEvent(complete_event);

            std::cout << "Mode transition completed: " << static_cast<int>(current)
                      << " -> " << static_cast<int>(target_mode) << std::endl;
        } else {
            // 过渡失败，恢复原模式
            current_mode_.store(current);
            onError("Mode transition failed");
        }

        return success;

    } catch (const std::exception& e) {
        current_mode_.store(current);
        onError("Exception during mode transition: " + std::string(e.what()));
        return false;
    }
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::validateModeTransition(HybridTrajectoryMode from_mode,
                                                         HybridTrajectoryMode to_mode) const {

    // 紧急模式可以从任何模式进入
    if (to_mode == HybridTrajectoryMode::EMERGENCY) {
        return true;
    }

    // 从紧急模式只能切换到空闲模式
    if (from_mode == HybridTrajectoryMode::EMERGENCY && to_mode != HybridTrajectoryMode::IDLE) {
        return false;
    }

    // 其他模式之间的切换都是允许的
    return true;
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::validateState(const MotionState<DOF>& state) const {
    // 检查数值有效性
    for (int i = 0; i < DOF; ++i) {
        if (!std::isfinite(state.position[i]) || !std::isfinite(state.velocity[i]) ||
            !std::isfinite(state.acceleration[i])) {
            return false;
        }
    }

    // 检查约束
    for (int i = 0; i < DOF; ++i) {
        if (std::abs(state.velocity[i]) > config_.motion_constraints.max_velocity[i] ||
            std::abs(state.acceleration[i]) > config_.motion_constraints.max_acceleration[i]) {
            return false;
        }
    }

    return true;
}

template<int DOF>
void HybridTrajectoryManager<DOF>::onSegmentCompleted(size_t segment_id) {
    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::SEGMENT_COMPLETED);
    event.segment_id = segment_id;
    event.message = "Segment " + std::to_string(segment_id) + " completed";
    notifyEvent(event);

    std::cout << "Segment " << segment_id << " completed" << std::endl;
}

template<int DOF>
void HybridTrajectoryManager<DOF>::onTransitionCompleted() {
    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::TRANSITION_COMPLETED);
    event.message = "Mode transition completed";
    notifyEvent(event);
}

template<int DOF>
void HybridTrajectoryManager<DOF>::onError(const std::string& error) {
    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::ERROR_OCCURRED);
    event.message = error;
    notifyEvent(event);

    std::cerr << "HybridTrajectoryManager Error: " << error << std::endl;
}

template<int DOF>
void HybridTrajectoryManager<DOF>::notifyEvent(const HybridManagerEvent<DOF>& event) {
    if (event_callback_) {
        event_callback_(event);
    }
}

template<int DOF>
void HybridTrajectoryManager<DOF>::notifyStateUpdate(const TrajectoryState<DOF>& state) {
    if (state_callback_) {
        state_callback_(state);
    }
}

template<int DOF>
void HybridTrajectoryManager<DOF>::resetTime() {
    auto now = std::chrono::steady_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
        now.time_since_epoch()).count() / 1000000.0;

    start_time_.store(timestamp);
    current_time_.store(0.0);
}

template<int DOF>
size_t HybridTrajectoryManager<DOF>::getTotalSegments() const {
    return segment_manager_->getTotalSegments();
}

template<int DOF>
void HybridTrajectoryManager<DOF>::printStatus() const {
    std::cout << getStatusString() << std::endl;
}

template<int DOF>
std::string HybridTrajectoryManager<DOF>::getStatusString() const {
    std::ostringstream oss;
    oss << "HybridTrajectoryManager Status:\n";
    oss << "  Mode: " << static_cast<int>(current_mode_.load()) << "\n";
    oss << "  Running: " << (is_running_.load() ? "Yes" : "No") << "\n";
    oss << "  Emergency: " << (emergency_stop_.load() ? "Yes" : "No") << "\n";
    oss << "  Time: " << current_time_.load() << "s\n";
    oss << "  Total Segments: " << getTotalSegments() << "\n";
    oss << "  Control Frequency: " << config_.control_frequency << " Hz";
    return oss.str();
}

template<int DOF>
bool HybridTrajectoryManager<DOF>::requestTrajectorySwitch(
    const std::vector<MotionState<DOF>>& new_waypoints,
    double transition_duration) {

    if (new_waypoints.empty()) {
        onError("Empty waypoints for trajectory switch");
        return false;
    }

    // 使用混合规划器的轨迹切换功能
    double duration = (transition_duration < 0) ? config_.default_transition_duration : transition_duration;

    bool success = hybrid_planner_->requestTrajectoryTransition(new_waypoints, duration);

    if (success) {
        HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::TRAJECTORY_UPDATED,
                                     "Trajectory switch requested");
        notifyEvent(event);

        std::cout << "Trajectory switch requested with " << new_waypoints.size()
                  << " waypoints" << std::endl;
    }

    return success;
}

template<int DOF>
void HybridTrajectoryManager<DOF>::clearEmergency() {
    emergency_stop_.store(false);

    // 切换回空闲模式
    current_mode_.store(HybridTrajectoryMode::IDLE);

    HybridManagerEvent<DOF> event(HybridManagerEvent<DOF>::MODE_CHANGED,
                                 "Emergency cleared, switched to IDLE");
    event.from_mode = HybridTrajectoryMode::EMERGENCY;
    event.to_mode = HybridTrajectoryMode::IDLE;
    notifyEvent(event);

    std::cout << "Emergency state cleared" << std::endl;
}

// 显式实例化
template class HybridTrajectoryManager<6>;
template class HybridTrajectoryManager<7>;
