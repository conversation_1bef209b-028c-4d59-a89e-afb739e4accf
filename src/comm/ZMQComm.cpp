#include "ZMQComm.h"
#include <iostream>
#include <cstring>
#include <thread>
#include <chrono>

ZMQComm::ZMQComm(bool is_server)
    : ctx_(1),
      is_server_(is_server),
      req_sock_(ctx_, zmq::socket_type::req),
      rep_sock_(ctx_, zmq::socket_type::rep),
      servo_push_(ctx_, zmq::socket_type::push),
      servo_pull_(ctx_, zmq::socket_type::pull),
      feedback_pub_(ctx_, zmq::socket_type::pub),
      feedback_sub_(ctx_, zmq::socket_type::sub),
      error_pub_(ctx_, zmq::socket_type::pub),
      error_sub_(ctx_, zmq::socket_type::sub)
{
    std::cout << "[ZMQ] Constructed, is_server=" << is_server_ << std::endl;
}

ZMQComm::~ZMQComm() {
    try {
        req_sock_.close();
        rep_sock_.close();
        servo_push_.close();
        servo_pull_.close();
        feedback_pub_.close();
        feedback_sub_.close();
        error_pub_.close();
        error_sub_.close();
        ctx_.close();
        is_conncet_ = false;
        std::cout << "[ZMQ] All sockets and context closed." << std::endl;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] Exception in destructor: " << e.what() << std::endl;
    }
}

bool ZMQComm::init() {
    try {
        if (is_server_) {
            // Server (hardware) side
            rep_sock_.bind(cmd_endpoint);
            std::cout << "[ZMQ] REP bind to " << cmd_endpoint << std::endl;

            // 配置PULL socket - Server拉取伺服命令
            // servo_pull_.set(zmq::sockopt::rcvhwm, 1);      // 只保留最新的1条消息
            // servo_pull_.set(zmq::sockopt::conflate, 1);    // 只保留最新消息，丢弃旧消息
            servo_pull_.bind(servo_data_endpoint);
            std::cout << "[ZMQ] PULL bind to " << servo_data_endpoint << std::endl;

            feedback_pub_.bind(feedback_data_endpoint); // 服务器发布RobotState
            std::cout << "[ZMQ] PUB bind to " << feedback_data_endpoint << std::endl;

            error_pub_.bind(error_endpoint); // 服务器发布错误信息
            std::cout << "[ZMQ] PUB bind to " << error_endpoint << std::endl;
        } else {
            // Client (robot) side
            req_sock_.connect(cmd_endpoint);
            std::cout << "[ZMQ] REQ connect to " << cmd_endpoint << std::endl;

            // 配置PUSH socket选项 - Client推送伺服命令
            // servo_push_.set(zmq::sockopt::sndhwm, 1);     // 只保留最新的1条消息
            // servo_push_.set(zmq::sockopt::linger, 0);     // 立即丢弃未发送的消息
            // servo_push_.set(zmq::sockopt::conflate, 1);   // 只保留最新消息，丢弃旧消息
            servo_push_.connect(servo_data_endpoint);
            std::cout << "[ZMQ] PUSH connect to " << servo_data_endpoint << std::endl;

            feedback_sub_.connect(feedback_data_endpoint);
            feedback_sub_.set(zmq::sockopt::subscribe, "");
            std::cout << "[ZMQ] SUB connect to " << feedback_data_endpoint << std::endl;

            error_sub_.connect(error_endpoint);
            error_sub_.set(zmq::sockopt::subscribe, "");
            std::cout << "[ZMQ] SUB connect to " << error_endpoint << std::endl;

            // 给 ZMQ 连接一些时间来完全建立，特别是 PUB/SUB 连接
            // PUB/SUB 连接需要更多时间来建立
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
        }
        std::cout<<"[ZMQ] Initialization successful"<<std::endl;
        is_conncet_ = true;
        return true;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] Exception in init: " << e.what() << std::endl;
        return false;
    }
}

void ZMQComm::close() {
    try {
        req_sock_.close();
        rep_sock_.close();
        servo_push_.close();
        servo_pull_.close();
        feedback_pub_.close();
        feedback_sub_.close();
        error_pub_.close();
        error_sub_.close();
        ctx_.close();
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] Close error: " << e.what() << std::endl;
    }
}

bool ZMQComm::isConnected() const {
    return is_conncet_;
}

std::string ZMQComm::requestReply(const std::string& request) {
    try {
        zmq::message_t req_msg(request.data(), request.size());
        req_sock_.send(req_msg, zmq::send_flags::none);
        std::cout << "[ZMQ] REQ sent: " << request << std::endl;
        zmq::message_t reply;
        auto res = req_sock_.recv(reply, zmq::recv_flags::none);
        if (!res) {
            std::cerr << "[ZMQ] REQ recv failed!" << std::endl;
            return "";
        }
        std::string reply_str(static_cast<char*>(reply.data()), reply.size());
        std::cout << "[ZMQ] REQ received reply: " << reply_str << std::endl;
        return reply_str;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] Exception in requestReply: " << e.what() << std::endl;
        return "";
    }
}

// ...existing code...
bool ZMQComm::sendRtData(const ServoCommand& cmd) {
    try {
        const std::string header = "SERVO_CMD:";
        size_t total_size = header.size() + sizeof(cmd);
        zmq::message_t msg(total_size);
        // 复制头部
        std::memcpy(msg.data(), header.data(), header.size());
        // 复制数据
        std::memcpy(static_cast<char*>(msg.data()) + header.size(), &cmd, sizeof(cmd));
        auto rc = servo_push_.send(msg, zmq::send_flags::dontwait);
        bool sent = rc.has_value();
        // std::cout << "[ZMQ] PUSH sendRtData(ServoCommand): timestamp=" << cmd.timestamp_us
        //           << " sent=" << sent << " size=" << sizeof(cmd) << std::endl;
        return sent;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] PUSH sendRtData(ServoCommand) error: " << e.what() << std::endl;
        return false;
    }
}

bool ZMQComm::recvRtData(const std::function<void(const ServoCommand&)>& callback) {
    try {
        zmq::message_t msg;
        const std::string header = "SERVO_CMD:";
        size_t expected_size = header.size() + sizeof(ServoCommand);
        if (servo_pull_.recv(msg, zmq::recv_flags::dontwait)) {
            if (msg.size() == expected_size) {
                // 校验头部
                if (std::memcmp(msg.data(), header.data(), header.size()) == 0) {
                    ServoCommand cmd;
                    std::memcpy(&cmd, static_cast<const char*>(msg.data()) + header.size(), sizeof(cmd));
                    //std::cout << "[ZMQ] PULL recv ServoCommand: timestamp=" << cmd.timestamp_us << std::endl;
                    callback(cmd);
                    return true;
                } else {
                    std::cerr << "[ZMQ] PULL recvRtData(ServoCommand) error: header mismatch" << std::endl;
                }
            }
        }
        return false;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] PULL recvRtData(ServoCommand) error: " << e.what() << std::endl;
        return false;
    }
}
// ...existing code...

// 实时状态通道（feedback_pub/sub，类型为RobotState）
bool ZMQComm::sendRtData(const RobotState& state) {
    try {
        zmq::message_t msg(sizeof(state));
        std::memcpy(msg.data(), &state, sizeof(state));
        auto rc = feedback_pub_.send(msg, zmq::send_flags::none);
        return rc.has_value();
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] sendRtData(RobotState) error: " << e.what() << std::endl;
        return false;
    }
}

bool ZMQComm::recvRtData(const std::function<void(const RobotState&)>& callback) {
    try {
        zmq::message_t msg;
        if (feedback_sub_.recv(msg, zmq::recv_flags::dontwait)) {
            if (msg.size() == sizeof(RobotState)) {
                RobotState state;
                std::memcpy(&state, msg.data(), sizeof(state));
                callback(state);
                return true;
            }
        }
        return false;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] recvRtData(RobotState) error: " << e.what() << std::endl;
        return false;
    }
}

// 错误通道（error_pub/sub，类型为string）
bool ZMQComm::sendError(const std::string& err) {
    try {
        zmq::message_t msg(err.data(), err.size());
        auto rc = error_pub_.send(msg, zmq::send_flags::none);
        return rc.has_value();
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] sendError error: " << e.what() << std::endl;
        return false;
    }
}

bool ZMQComm::recvError(const std::function<void(const std::string&)>& callback) {
    try {
        zmq::message_t msg;
        if (error_sub_.recv(msg, zmq::recv_flags::dontwait)) {
            std::string err(static_cast<char*>(msg.data()), msg.size());
            callback(err);
            return true;
        }
        return false;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] recvError error: " << e.what() << std::endl;
        return false;
    }
}

// REP/REQ命令通道（server端处理）
std::string ZMQComm::recvReply() {
    try {
        zmq::message_t request;
        auto res = rep_sock_.recv(request, zmq::recv_flags::dontwait);
        if (!res) {
            // 非阻塞模式下没有消息是正常的，不需要打印错误
            return "";
        }
        std::string req_str(static_cast<char*>(request.data()), request.size());
        std::cout << "[ZMQ] REP received: " << req_str << std::endl;
        std::string reply_str;
        // 普通业务处理
        reply_str = "OK: " + req_str;

        zmq::message_t reply(reply_str.data(), reply_str.size());
        rep_sock_.send(reply, zmq::send_flags::none);
        std::cout << "[ZMQ] REP sent reply: " << reply_str << std::endl;
        return req_str;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] Exception in recvReply: " << e.what() << std::endl;
        return "";
    }
}

// 兼容CommBase的string反馈接口
bool ZMQComm::sendFeedbackData(const std::string& data) {
    try {
        zmq::message_t msg(data.data(), data.size());
        auto rc = feedback_pub_.send(msg, zmq::send_flags::none);
        return rc.has_value();
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] sendFeedbackData(string) error: " << e.what() << std::endl;
        return false;
    }
}

bool ZMQComm::recvFeedbackData(const std::function<void(const std::string&)>& callback) {
    try {
        zmq::message_t msg;
        if (feedback_sub_.recv(msg, zmq::recv_flags::dontwait)) {
            std::string data(static_cast<char*>(msg.data()), msg.size());
            callback(data);
            return true;
        }
        return false;
    } catch (const zmq::error_t& e) {
        std::cerr << "[ZMQ] recvFeedbackData(string) error: " << e.what() << std::endl;
        return false;
    }
}

uint64_t ZMQComm::getCurrentTimestamp() const {
    return std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()
    ).count();
}

std::string ZMQComm::getConnectionInfo() const {
    return std::string("ZMQ: ") + cmd_endpoint + ", " + servo_data_endpoint + ", " + feedback_data_endpoint + ", " + error_endpoint;
}