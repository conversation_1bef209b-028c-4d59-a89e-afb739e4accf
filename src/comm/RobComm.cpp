#include "RobComm.h"
#include "CommFactory.h"
#include <iostream>
#include <chrono>
#include <atomic>

RobComm::RobComm(CommType type)
    : current_type_(type)
    , stop_flag_(false)
{
    // ZMQ 模式下 is_server=false，FIFO 模式下 is_server=false
    comm_ = CommFactory::createComm(type, false);
}

RobComm::~RobComm() {
    close();
}

bool RobComm::init() {
    if (!comm_) {
        std::cerr << "Failed to create communication instance" << std::endl;
        return false;
    }
    
    if (!comm_->init()) {
        std::cerr << "Failed to initialize communication" << std::endl;
        return false;
    }
    is_connected_ = true;
    stop_flag_ = false;
    recv_thread_ = std::thread(&RobComm::recv_loop, this);
    send_thread_ = std::thread(&RobComm::send_loop, this);
    
    std::cout << "RobComm initialized with " 
              << (current_type_ == CommType::ZMQ ? "ZMQ" : "FIFO") 
              << " communication" << std::endl;
    return true;
}

void RobComm::close() {
    stop_flag_ = true;
    
    if (recv_thread_.joinable()) {
        recv_thread_.join();
    }
    if (send_thread_.joinable()) {
        send_thread_.join();
    }
    
    if (comm_) {
        comm_->close();
    }
}

bool RobComm::isConnected() const {
    return is_connected_;
}

std::string RobComm::requestReply(const std::string& cmd) {
    if (!comm_) return "";
    return comm_->requestReply(cmd);
}

bool RobComm::sendRtData(const ServoCommand& cmd) {
    if (!comm_) return false;

    // 同步发送
    comm_->sendRtData(cmd);
    // // 将命令添加到队列
    // {
    //     std::lock_guard<std::mutex> lk(queue_mtx_);
    //     cmd_queue_.push(cmd);
    // }
    
    // // 打印命令信息
    // std::cout << "RobComm: Added command to queue - "
    //           << "joints: [" << cmd.position[0] << ", " 
    //           << cmd.position[1] << ", " << cmd.position[2] << ", "
    //           << cmd.position[3] << ", " << cmd.position[4] << ", "
    //           << cmd.position[5]  << "]"
    //           << " timestamp: " << cmd.timestamp_us << std::endl;
    
    return true;
}

bool RobComm::recvRtData(const std::function<void(const RobotState&)>& callback) {
    if (!comm_) return false;
    rt_data_callback_ = callback;
    
    // 设置接收回调，打印接收到的状态信息
    auto wrapped_callback = [this, callback](const RobotState& state) {
        // std::cout << "RobComm: Received robot state - "
        //           << "position: [" << state.position[0] << ", " 
        //           << state.position[1] << ", " << state.position[2] << ", "
        //           << state.position[3] << ", " << state.position[4] << ", "
        //           << state.position[5] << "]"
        //           << " velocity: [" << state.velocity[0] << ", " 
        //           << state.velocity[1] << ", " << state.velocity[2] << ", "
        //           << state.velocity[3] << ", " << state.velocity[4] << ", "
        //           << state.velocity[5] << "]"
        //           << " current: [" << state.current[0] << ", " 
        //           << state.current[1] << ", " << state.current[2] << ", "
        //           << state.current[3] << ", " << state.current[4] << ", "
        //           << state.current[5] << "]"
        //           << " servo_status: " << state.servo_status
        //           << " timestamp: " << state.timestamp_us << std::endl;
        std::cout << "RobComm: Received robot state - "<<std::endl;
        // 更新最新状态
        {
            std::lock_guard<std::mutex> lk(state_mtx_);
            latest_state_ = state;
        }
        
        // 调用原始回调
        if (callback) {
            callback(state);
        }
    };
    
    return comm_->recvRtData(wrapped_callback);
}

bool RobComm::sendFeedback(const std::string& feedback) {
    if (!comm_) return false;
    return comm_->sendFeedbackData(feedback);
}

bool RobComm::recvFeedback(const std::function<void(const std::string&)>& callback) {
    if (!comm_) return false;
    return comm_->recvFeedbackData(callback);
}

int RobComm::init_robot() {
    std::string result = requestReply("SYS_INIT");
    return result.find("OK") != std::string::npos ? 0 : -1;
}

int RobComm::get_state() {
    std::string result = requestReply("GET_STATE");
    return result.find("OK") != std::string::npos ? 0 : -1;
}

int RobComm::reset() {
    std::string result = requestReply("RESET");
    return result.find("OK") != std::string::npos ? 0 : -1;
}

int RobComm::query_origin() {
    std::string result = requestReply("QUERY_ORIGIN");
    return result.find("OK") != std::string::npos ? 0 : -1;
}

int RobComm::homing() {
    std::string result = requestReply("HOMING");
    return result.find("OK") != std::string::npos ? 0 : -1;
}

int RobComm::start_servo(int mode, double gain, double smooth, int period) {
    // 构造命令字符串：START_SERVO mode gain smooth period
    std::ostringstream oss;
    oss << "START_SERVO " << mode << " " << gain << " " << smooth << " " << period;
    
    std::string result = requestReply(oss.str());
    return result.find("OK") != std::string::npos ? 0 : -1;
}

int RobComm::stop_servo() {
    std::string result = requestReply("STOP_SERVO");
    return result.find("OK") != std::string::npos ? 0 : -1;
}

void RobComm::switchCommType(CommType type) {
    if (type == current_type_) return;
    
    std::cout << "Switching communication type from " 
              << (current_type_ == CommType::ZMQ ? "ZMQ" : "FIFO")
              << " to " << (type == CommType::ZMQ ? "ZMQ" : "FIFO") << std::endl;
    
    close();
    current_type_ = type;
    comm_ = CommFactory::createComm(type, false);
    init();
}

std::string RobComm::get_connection_info() const {
    if (!comm_) return "No communication instance";
    return comm_->getConnectionInfo();
}

RobotState RobComm::get_latest_state() const {
    std::lock_guard<std::mutex> lk(state_mtx_);
    return latest_state_;
}

void RobComm::recv_loop() {
    while (!stop_flag_) {
        if (comm_ && rt_data_callback_) {
            // 设置接收回调，打印接收到的状态信息
            auto wrapped_callback = [this](const RobotState& state) {
                // std::cout << "RobComm: Received robot state in loop - "
                //            << "position: [" << state.position[0] << ", " 
                //            << state.position[1] << ", " << state.position[2] << ", "
                //            << state.position[3] << ", " << state.position[4] << ", "
                //            << state.position[5] << "]"
                //            << " timestamp: " << state.timestamp_us << std::endl;
                
                // 更新最新状态
                {
                    std::lock_guard<std::mutex> lk(state_mtx_);
                    latest_state_ = state;
                }
                
                // 调用原始回调
                if (rt_data_callback_) {
                    rt_data_callback_(state);
                }
            };
            
            comm_->recvRtData(wrapped_callback);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void RobComm::send_loop() {
    while (!stop_flag_) {
        ServoCommand cmd;
        bool has_cmd = false;
        
        {
            std::lock_guard<std::mutex> lk(queue_mtx_);
            if (!cmd_queue_.empty()) {
                cmd = cmd_queue_.front();
                cmd_queue_.pop();
                has_cmd = true;
            }
        }
        
        if (has_cmd && comm_) {
            cmd.timestamp_us = get_timestamp_us();
            std::cout << "[RobComm] send_loop: sending ServoCommand, timestamp: " << cmd.timestamp_us << std::endl;
            auto start = std::chrono::steady_clock::now();
            
            comm_->sendRtData(cmd);
            auto end = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            auto sleep_time = cmd.duration_ms > elapsed ? cmd.duration_ms - elapsed : 0;
            //同步发送，不sleep
            // if(sleep_time>50) sleep_time = 50;
            // std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time));
            // std::cout<<"[RobComm] send_loop: sendRtData finished, timestamp: " << sleep_time << std::endl;//延时有问题。长了，收不到数据
        } else {
            // 无指令时休眠 10ms
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
}

uint64_t RobComm::get_timestamp_us() const {
    return std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()
    ).count();
} 