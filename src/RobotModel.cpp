#include "RobotModel.h"
#include <iostream>
#include <stdexcept>
#include <cmath>
#include <random>
#include <chrono>
//Found Pinocchio: 3.4.0
namespace robot_infra {

RobotModel::RobotModel(const std::string& urdf_path,
                       const std::string& end_effector_frame_name)
    : urdf_path_(urdf_path)
    , end_effector_frame_name_(end_effector_frame_name)
    , end_effector_frame_id_(0)
    , nq_(0)
    , workspace_limits_set_(false)
    , initialized_(false) {
}

bool RobotModel::initialize() {
    try {
        // 从 URDF 文件构建模型
        pinocchio::urdf::buildModel(urdf_path_, model_);
        data_ = pinocchio::Data(model_);

        // 1) 统计可动关节自由度
        nq_ = model_.nv;  // 对外接口的关节数量

        std::cout << "Debug: model_.nq = " << model_.nq << ", model_.nv = " << model_.nv << std::endl;

        // 2) 分配限位容器
        q_min_   = VectorXd::Zero(nq_);
        q_max_   = VectorXd::Zero(nq_);
        dq_max_  = VectorXd::Zero(nq_);
        ddq_max_ = VectorXd::Constant(nq_, 10.0);

        // 3) 填充限位
        int idx = 0;
        for (auto i = 1u; i < model_.joints.size(); ++i) {
            int nj = model_.joints[i].nq();
            for (int k = 0; k < nj; ++k, ++idx) {
                int mi = model_.joints[i].idx_q() + k;
                q_min_[idx]  = model_.lowerPositionLimit[mi];
                q_max_[idx]  = model_.upperPositionLimit[mi];
                dq_max_[idx] = model_.velocityLimit[mi];
            }
        }

        // 设置默认工作空间限位
        workspace_min_ << -0.001, -0.001, -0.001, -0.001, -0.001, -0.001;
        workspace_max_ <<  0.5,  0.174, 0.05,  M_PI,  M_PI,  M_PI;
        workspace_limits_set_ = true;
        // 查找末端执行器 frame
        if (!end_effector_frame_name_.empty() && model_.existFrame(end_effector_frame_name_)) {
            end_effector_frame_id_ = model_.getFrameId(end_effector_frame_name_);
            std::cout << "end_effector_frame_name_: "<<end_effector_frame_name_<<
            " ,   id_: "<<end_effector_frame_id_<<std::endl;
        } else {
            if (!end_effector_frame_name_.empty()) {
                std::cerr << "Warning: Frame '" << end_effector_frame_name_
                          << "' not found. Using last frame.\n";
            }
            end_effector_frame_id_ = model_.frames.size() - 1;
        }

        initialized_ = true;
        std::cout << "RobotModel initialized with " << nq_ << " DOF.\n";


        std::cout << "Number of joints: " << model_.njoints << std::endl;
        std::cout << "Number of frames: " << model_.nframes << std::endl;
        std::cout << "End-effector frame id: " << end_effector_frame_id_ << std::endl;
        std::cout << "End-effector frame name: " << model_.frames[end_effector_frame_id_].name << std::endl;

        
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize RobotModel: " << e.what() << std::endl;
        return false;
    }
}

// 私有：检查初始化及尺寸
void RobotModel::checkInitAndSize(const VectorXd& q) const {
    if (!initialized_) {
        throw std::runtime_error("RobotModel not initialized");
    }
    if (q.size() != nq_) {
        throw std::invalid_argument("Joint vector size mismatch: expected " + 
            std::to_string(nq_) + " but got " + 
            std::to_string(q.size()));
    }
}

// 私有：将活动关节映射到完整配置
RobotModel::VectorXd RobotModel::mapToFullConfiguration(const VectorXd& q_active) const {
    checkInitAndSize(q_active);
    VectorXd q_full = VectorXd::Zero(model_.nq);
    int idx = 0;
    for (auto i = 1u; i < model_.joints.size(); ++i) {
        int nj = model_.joints[i].nq();
        for (int k = 0; k < nj; ++k) {
            q_full[model_.joints[i].idx_q() + k] = q_active[idx++];
        }
    }
    return q_full;
}


RobotModel::VectorXd RobotModel::generatePositionUniform() const {
    VectorXd q(nq_);

    static std::random_device rd;
    static std::mt19937 gen(rd());

    for (int i = 0; i < nq_; ++i) {
        std::uniform_real_distribution<double> dis(q_min_[i], q_max_[i]);
        q[i] = dis(gen);
    }

    return q;
}

RobotModel::Vector6d RobotModel::forwardKinematics(const VectorXd& q) {
    checkInitAndSize(q);

    auto q_full = mapToFullConfiguration(q);
    pinocchio::forwardKinematics(model_, data_, q_full);
    pinocchio::updateGlobalPlacements(model_, data_);
    pinocchio::updateFramePlacements(model_, data_);

    return se3ToPose(data_.oMf[end_effector_frame_id_]);
}

RobotModel::SE3 RobotModel::getEndEffectorTransform(const VectorXd& q) {
    checkInitAndSize(q);
    auto q_full = mapToFullConfiguration(q);
    pinocchio::forwardKinematics(model_, data_, q_full);
    pinocchio::updateGlobalPlacements(model_, data_);
    pinocchio::updateFramePlacements(model_, data_);
    return data_.oMf[end_effector_frame_id_];
}

RobotModel::MatrixXd RobotModel::computeJacobian(const VectorXd& q,
                                     pinocchio::ReferenceFrame rf) {
    checkInitAndSize(q);
    auto q_full = mapToFullConfiguration(q);
    pinocchio::forwardKinematics(model_, data_, q_full);
    pinocchio::updateGlobalPlacements(model_, data_);
    pinocchio::updateFramePlacements(model_, data_);

    MatrixXd J_full(6, model_.nv);
    // Use frame Jacobian instead of joint Jacobian
    pinocchio::computeFrameJacobian(model_, data_, q_full, end_effector_frame_id_, rf, J_full);
    //pinocchio::computeJointJacobian(model_, data_, q_full,model_.frames[end_effector_frame_id_].parent, J);

    // 提取活动关节列
    MatrixXd J(6, nq_);
    int idx = 0;
    for (auto i = 1u; i < model_.joints.size(); ++i) {
        int nv = model_.joints[i].nv();
        for (int k = 0; k < nv; ++k) {
            J.col(idx++) = J_full.col(model_.joints[i].idx_v() + k);
        }
    }
    return J;
}

// 优化的雅可比伪逆实现
RobotModel::MatrixXd RobotModel::updateJacobianInverse(const MatrixXd& jacobian,
                                                      double lambda,
                                                      bool doSvd) {
    MatrixXd jacobianInverse;

    if (doSvd) {
        // 简化的SVD方法
        jacobianInverse.setZero(jacobian.cols(), jacobian.rows());

        Eigen::JacobiSVD<MatrixXd> svd(jacobian, Eigen::ComputeFullU | Eigen::ComputeFullV);

        double wMin = svd.singularValues().minCoeff();
        double lambdaSqr = wMin < 1.0e-9 ?
            (1.0 - std::pow((wMin / 1.0e-9), 2)) * std::pow(lambda, 2) : 0.0;

        for (int i = 0; i < svd.nonzeroSingularValues(); ++i) {
            double sv = svd.singularValues()(i);
            double weight = sv / (sv * sv + lambdaSqr);
            jacobianInverse.noalias() += weight * svd.matrixV().col(i) * svd.matrixU().col(i).transpose();
        }
    } else {
        // 优化的阻尼最小二乘方法
        MatrixXd JtJ = jacobian.transpose() * jacobian;
        MatrixXd damping_matrix = lambda * lambda * MatrixXd::Identity(JtJ.rows(), JtJ.cols());

        // 使用LDLT分解提高数值稳定性和速度
        jacobianInverse = (JtJ + damping_matrix).ldlt().solve(jacobian.transpose());
    }

    return jacobianInverse;
}



// 检查关节角度是否在有效范围内（基于参考代码）
bool RobotModel::isValid(const VectorXd& q) const {
    if (q.size() != nq_) {
        return false;
    }

    for (int i = 0; i < q.size(); ++i) {
        if (q[i] < q_min_[i] || q[i] > q_max_[i]) {
            return false;
        }
    }

    return true;
}

// 计算两个关节配置之间的变换距离（基于参考代码）
double RobotModel::transformedDistance(const VectorXd& q1, const VectorXd& q2) const {
    VectorXd diff = q2 - q1;
    return diff.squaredNorm();  // 返回距离的平方
}

// 在两个关节配置之间进行插值（基于参考代码，支持环绕关节）
void RobotModel::interpolate(const VectorXd& q1, const VectorXd& q2, double delta, VectorXd& q_result) const {
    assert(q1.size() == nq_);
    assert(q2.size() == nq_);
    assert(delta >= 0);
    assert(q_result.size() == nq_);

    // 计算距离并确定插值比例
    VectorXd diff = q2 - q1;
    double distance = diff.norm();

    double alpha = (distance > delta) ? (delta / distance) : 1.0;

    // 对每个关节进行插值
    for (int i = 0; i < nq_; ++i) {
        // 检查是否为环绕关节（如旋转关节）
        bool wraparound = false;
        double range = q_max_[i] - q_min_[i];

        // 如果关节范围接近2π，认为是环绕关节
        if (std::abs(range - 2 * M_PI) < 0.1) {
            wraparound = true;
        }

        if (wraparound) {
            double diff_direct = std::abs(q2[i] - q1[i]);
            double diff_wrap = std::abs(range - diff_direct);

            if (diff_wrap < diff_direct) {
                // 使用环绕路径更短
                if (q1[i] > q2[i]) {
                    q_result[i] = (1 - alpha) * q1[i] + alpha * (q2[i] + range);
                } else {
                    q_result[i] = (1 - alpha) * (q1[i] + range) + alpha * q2[i];
                }

                // 归一化到有效范围
                while (q_result[i] > q_max_[i]) {
                    q_result[i] -= range;
                }
                while (q_result[i] < q_min_[i]) {
                    q_result[i] += range;
                }
            } else {
                // 使用直接路径
                q_result[i] = (1 - alpha) * q1[i] + alpha * q2[i];
            }
        } else {
            // 非环绕关节，使用线性插值
            q_result[i] = (1 - alpha) * q1[i] + alpha * q2[i];
        }
    }
}

bool RobotModel::inverseKinematics(VectorXd& q_result,
    const Vector6d& target_pose,
    const VectorXd& q_init,
    int max_iter,
    double tol) {

    bool debug = false;
    checkInitAndSize(q_init);

    VectorXd q = q_init;

    // 恢复原始阻尼参数
    double damping_max = 1e-1;
    double damping_min = 5e-2;
    double damping = 0.02;

    double prev_error_norm = std::numeric_limits<double>::infinity();//maax
    int stagnation_count = 0;

    Eigen::Vector3d target_translation = target_pose.head<3>();
    Eigen::Vector3d target_euler = target_pose.tail<3>();  // [rz, ry, rx]

    // 将欧拉角转换为旋转矩阵，注意顺序是 ZYX (rz, ry, rx)
    Eigen::Matrix3d target_rotation =
        (Eigen::AngleAxisd(target_euler[0], Eigen::Vector3d::UnitZ()) *
         Eigen::AngleAxisd(target_euler[1], Eigen::Vector3d::UnitY()) *
         Eigen::AngleAxisd(target_euler[2], Eigen::Vector3d::UnitX())).toRotationMatrix();

    SE3 target_SE3(target_rotation, target_translation);
   
    // 恢复原始多重启动策略
    const int max_restart = 5;
    for (int attempt = 0; attempt < max_restart; ++attempt) {
        // 更智能的初始值选择
        if (attempt == 0) {
            q = q_init;  // 第一次使用提供的初始值
        } else if (attempt <= 1) {
            q = VectorXd::Zero(nq_);  // 第二次使用零位 很重要
        } else {
            q = generatePositionUniform();  // 后续使用随机值
        }

        if (debug)
        {
            std::cout << "[IK] 尝试次数: " << attempt << std::endl;
            std::cout << "[IK] 初值角度: "<< q.transpose() << std::endl;
        }
        
        double prev_error_norm = std::numeric_limits<double>::max();

        for (int it = 0; it < max_iter; ++it) {
            auto q_full = mapToFullConfiguration(q);
            pinocchio::forwardKinematics(model_, data_, q_full);
            pinocchio::updateFramePlacements(model_, data_);

            SE3 current_SE3 = data_.oMf[end_effector_frame_id_];

            // Calculate velocity-based error
            Eigen::Vector3d pos_error = target_SE3.translation() - current_SE3.translation();
            Eigen::Vector3d ori_error = pinocchio::log3(target_SE3.rotation() * current_SE3.rotation().transpose());
            Vector6d error;
            error.head<3>() = pos_error;
            error.tail<3>() = ori_error;
           
            double error_norm = error.norm();
            if (debug)
            {
                std::cout << "[IK] Iter " << it 
                << " | error : " << error.transpose() 
                << " | norm: " << error_norm << std::endl;
                
                Eigen::Vector3d current_aa = pinocchio::log3(current_SE3.rotation());
                std::cout << "[IK] 目标位置: " << target_SE3.translation().transpose() << "   旋转: " << target_euler.transpose()<< std::endl;
                std::cout << "[IK] 当前位置: " << current_SE3.translation().transpose() << "   旋转: " << current_aa.transpose()<< std::endl;
                std::cout << "[IK] 误差: " << error.transpose() << std::endl;   
                std::cout << "[IK] 初值角度: "<< q.transpose() << std::endl;
                std::cout <<"[IK] 误差平方和: " <<error.squaredNorm()<<std::endl;
            }
            
            
            // 恢复原始收敛检测
            if (error.squaredNorm() < std::pow(tol, 2))
            {
                // 归一化并检查解的有效性
                q = normalizeAndLimitJoints(q);

                if (isValid(q))
                {
                    if(debug)
                        std::cout <<"IK收敛成功! 迭代次数: " << it << " 尝试次数: " << attempt << std::endl;
                    q_result = q;
                    return true;
                }
                else
                {
                    std::cout << "[IK] 解超出关节限位，继续迭代..." << std::endl;
                }
            }

            if (std::abs(prev_error_norm - error_norm) < tol * 0.01) {
                stagnation_count++;
                if (stagnation_count > 3) {
                    damping = std::min(damping * 1.5, damping_max);  // 缓慢增大
                    stagnation_count = 0;
                    //std::cout << "[IK] 检测到停滞，增加阻尼: " << damping << std::endl;
                }
            } else {
                stagnation_count = 0;
                damping = std::max(damping * 0.7, damping_min);
            }

            prev_error_norm = error_norm;

            // 计算雅可比矩阵 - LOCAL_WORLD_ALIGNED 表示线速度在世界坐标系，角速度在局部坐标系
            MatrixXd J = computeJacobian(q, pinocchio::LOCAL_WORLD_ALIGNED);

            // 简要显示雅可比条件数（数值稳定性判断）
            double cond_J = Eigen::JacobiSVD<MatrixXd>(J).singularValues()(0) /
            Eigen::JacobiSVD<MatrixXd>(J).singularValues().tail(1)(0);
            //std::cout << "[IK] 雅可比 number: " << cond_J << std::endl;

            //--- 如果 Jacobian 条件数太差，强制启用大阻尼 ---
            if (cond_J > 1e4) {
                damping = std::min(std::max(damping, 0.05), damping_max);  // 提升稳定性
                // std::cout << "[IK] Jacobian condition poor (" << cond_J << "), 增大阻尼至: " << damping << std::endl;
            }

            // 使用雅可比伪逆求解关节速度
            // dq = J^(-1) * dx，其中：
            VectorXd dq = updateJacobianInverse(J,damping,false) * error;//0.017

            // 恢复原始步长控制
            double max_step = 0.2;
            if (dq.norm() > max_step) {
                dq = dq * (max_step / dq.norm());
            }

            // 简单的步长控制
            double step_size = std::min(1.0, std::max(0.05, 1.0 / (1.0 + error_norm)));
            
            // 计算新的关节配置
            VectorXd q2 = q + step_size * dq;  

            // 应用关节限位
            q2 = clampJointLimits(q2);

            // 检查步长是否过大，如果是则进行插值（基于参考代码）
            double delta = 0.1;  // 最大允许步长
            if (transformedDistance(q, q2) > std::pow(delta, 2)) {
                VectorXd q_interpolated(q.size());
                interpolate(q, q2, delta, q_interpolated);
                q = q_interpolated;
                //std::cout << "[IK] 步长过大，进行插值限制" << std::endl;
            } else {
                q = q2;
            }

            if (debug)
            {
                std::cout << "[IK] dq: " << dq.transpose() << std::endl;
                std::cout<<" step * dq: "<<step_size * dq.transpose()<<std::endl;
                std::cout << "[IK] 更新后关节角: " << q.transpose() << std::endl;
                std::cout << "------------------------------------------------------" << std::endl;
            }
            
        }
        if (debug)
            std::cout << "[IK] 当前尝试未收敛，尝试下一个初值..." << std::endl;    
    }
    if (debug)
    {
        std::cout << "[IK] 未收敛! 最大迭代次数: " << max_iter << std::endl;
        std::cout << "[IK] 最终误差: " << prev_error_norm << std::endl;
        std::cout << "[IK] 最终关节角度: " << q.transpose() << std::endl;
    }
    
    q_result = q;  // 即使未收敛，也返回最后的结果
    return false;
}



RobotModel::VectorXd RobotModel::normalizeAndLimitJoints(const VectorXd& q) const {
    VectorXd normalized_q = q;

    for (int i = 0; i < nq_; ++i) {
        // Get joint type (skip universe joint at index 0)
        int joint_id = i + 1;
        if (joint_id >= static_cast<int>(model_.joints.size())) {
            continue;
        }

        std::string joint_type = model_.joints[joint_id].shortname();

        // Normalize revolute joints to [-π, π] range
        if (joint_type == "JointModelRX" || joint_type == "JointModelRY" || joint_type == "JointModelRZ") {
            // Normalize angle to [-π, π] range
            normalized_q[i] = std::fmod(normalized_q[i] + M_PI, 2 * M_PI) - M_PI;
        }

        // Apply joint limits
        normalized_q[i] = std::max(q_min_[i], std::min(q_max_[i], normalized_q[i]));
    }

    return normalized_q;
}

RobotModel::Vector6d RobotModel::se3ToPose(const SE3& tf) const {
    Vector6d p;
    p.head<3>() = tf.translation();
    p.tail<3>() = tf.rotation().eulerAngles(2,1,0);
    // std::cout<<"[se3ToPose] rot: "<< std::endl<< tf.rotation()<<std::endl;
    // std::cout<<"[se3ToPose] pose: "<< p.transpose()<<std::endl;
    return p;
}

RobotModel::SE3 RobotModel::poseToSE3(const Vector6d& pose) const {
    Eigen::Vector3d t = pose.head<3>();
    Eigen::Vector3d r = pose.tail<3>();
    SE3 tf = SE3(
        Eigen::AngleAxisd(r[2], Eigen::Vector3d::UnitZ()) *
        Eigen::AngleAxisd(r[1], Eigen::Vector3d::UnitY()) *
        Eigen::AngleAxisd(r[0], Eigen::Vector3d::UnitX()),
        t);
    return tf;
}

RobotModel::Vector6d RobotModel::computePoseError(const SE3& cur, const SE3& tgt) const {
    Vector6d err;
    err.head<3>() = tgt.translation() - cur.translation();
    SE3 delta = tgt * cur.inverse();
    err.tail<3>() = pinocchio::log3(delta.rotation());
    return err;
}

// ============================================================================
// 统一限位检查和处理方法
// ============================================================================

bool RobotModel::validateAndClampJointCommand(VectorXd& jointRad, const std::string& command_name) const {
    if (!initialized_) {
        return true; // 如果模型未初始化，跳过检查
    }

    // 检查关节限位
    bool clamped = false;
    for (int i = 0; i < jointRad.size() && i < nq_; ++i) {
        if (jointRad[i] < q_min_[i]) {
            std::cout << "Warning: " << command_name << " joint " << i << " below limit: "
                      << jointRad[i] << " < " << q_min_[i] << ", clamping" << std::endl;
            jointRad[i] = q_min_[i];
            clamped = true;
        } else if (jointRad[i] > q_max_[i]) {
            std::cout << "Warning: " << command_name << " joint " << i << " above limit: "
                      << jointRad[i] << " > " << q_max_[i] << ", clamping" << std::endl;
            jointRad[i] = q_max_[i];
            clamped = true;
        }
    }

    return true;
}

bool RobotModel::validateAndClampCartesianCommand(Vector6d& pose, const std::string& command_name) const {

    if (!initialized_) {
        return true; // 如果模型未初始化，跳过检查
    }
    // 检查工作空间限位
    bool clamped = false;
    if (workspace_limits_set_) {
        for (int i = 0; i < 6; ++i) {
            if (pose[i] < workspace_min_[i]) {
                std::cout << "Warning: " << command_name << " pose[" << i << "] below workspace limit: "
                          << pose[i] << " < " << workspace_min_[i] << ", clamping" << std::endl;
                pose[i] = workspace_min_[i];
                clamped = true;
            } else if (pose[i] > workspace_max_[i]) {
                std::cout << "Warning: " << command_name << " pose[" << i << "] above workspace limit: "
                          << pose[i] << " > " << workspace_max_[i] << ", clamping" << std::endl;
                pose[i] = workspace_max_[i];
                clamped = true;
            }
        }
    }
    return true;
}

// ============================================================================
// 限位设置方法
// ============================================================================

void RobotModel::setJointLimits(const VectorXd& q_min, const VectorXd& q_max,
                                const VectorXd& dq_max, const VectorXd& ddq_max) {
    if (q_min.size() != nq_ || q_max.size() != nq_ ||
        dq_max.size() != nq_ || ddq_max.size() != nq_) {
        throw std::invalid_argument("Joint limit vector size mismatch");
    }

    q_min_ = q_min;
    q_max_ = q_max;
    dq_max_ = dq_max;
    ddq_max_ = ddq_max;
}

void RobotModel::setWorkspaceLimits(const Vector6d& workspace_min, const Vector6d& workspace_max) {
    workspace_min_ = workspace_min;
    workspace_max_ = workspace_max;
    workspace_limits_set_ = true;
}

// ============================================================================
// 获取器方法
// ============================================================================

std::pair<RobotModel::VectorXd, RobotModel::VectorXd> RobotModel::getJointLimits() const {
    return std::make_pair(q_min_, q_max_);
}

std::pair<RobotModel::Vector6d, RobotModel::Vector6d> RobotModel::getWorkspaceLimits() const {
    return std::make_pair(workspace_min_, workspace_max_);
}

std::string RobotModel::getModelInfo() const {
    std::string info = "Robot Model Information:\n";
    info += "  URDF Path: " + urdf_path_ + "\n";
    info += "  Joints: " + std::to_string(nq_) + "\n";
    info += "  End-effector frame: " + end_effector_frame_name_ + "\n";
    info += "  Frames: " + std::to_string(model_.frames.size()) + "\n";
    return info;
}

// ============================================================================
// 动力学计算函数
// ============================================================================

RobotModel::MatrixXd RobotModel::computeInertiaMatrix(const VectorXd& q) {
    checkInitAndSize(q);

    auto q_full = mapToFullConfiguration(q);
    pinocchio::crba(model_, data_, q_full);
    data_.M.triangularView<Eigen::StrictlyLower>() = data_.M.transpose().triangularView<Eigen::StrictlyLower>();

    return data_.M;
}

RobotModel::VectorXd RobotModel::computeCoriolisForces(const VectorXd& q, const VectorXd& dq) {
    checkInitAndSize(q);
    checkInitAndSize(dq);

    auto q_full = mapToFullConfiguration(q);
    auto dq_full = mapToFullConfiguration(dq);

    pinocchio::computeCoriolisMatrix(model_, data_, q_full, dq_full);
    return data_.C * dq_full;
}

RobotModel::VectorXd RobotModel::computeGravityForces(const VectorXd& q) {
    checkInitAndSize(q);

    auto q_full = mapToFullConfiguration(q);
    pinocchio::computeGeneralizedGravity(model_, data_, q_full);

    return data_.g;
}

RobotModel::VectorXd RobotModel::inverseDynamics(const VectorXd& q, const VectorXd& dq, const VectorXd& ddq) {
    checkInitAndSize(q);
    checkInitAndSize(dq);
    checkInitAndSize(ddq);

    auto q_full = mapToFullConfiguration(q);
    auto dq_full = mapToFullConfiguration(dq);
    auto ddq_full = mapToFullConfiguration(ddq);

    return pinocchio::rnea(model_, data_, q_full, dq_full, ddq_full);
}

RobotModel::VectorXd RobotModel::forwardDynamics(const VectorXd& q, const VectorXd& dq, const VectorXd& tau) {
    checkInitAndSize(q);
    checkInitAndSize(dq);
    checkInitAndSize(tau);

    auto q_full = mapToFullConfiguration(q);
    auto dq_full = mapToFullConfiguration(dq);
    auto tau_full = mapToFullConfiguration(tau);

    return pinocchio::aba(model_, data_, q_full, dq_full, tau_full);
}

// ============================================================================
// 限位检查函数
// ============================================================================

bool RobotModel::checkJointLimits(const VectorXd& q) const {
    if (q.size() != nq_) {
        return false;
    }

    for (int i = 0; i < nq_; ++i) {
        if (q[i] < q_min_[i] || q[i] > q_max_[i]) {
            return false;
        }
    }
    return true;
}

bool RobotModel::checkWorkspaceLimits(const Vector6d& pose) const {
    // 简单的工作空间检查 - 可以根据具体机器人调整
    Eigen::Vector3d position = pose.head<3>();

    // 检查位置是否在合理范围内
    double max_reach = 2.0;  // 最大到达距离，可以根据机器人调整
    if (position.norm() > max_reach) {
        return false;
    }

    // 检查Z轴高度
    if (position[2] < -0.5 || position[2] > 2.0) {
        return false;
    }

    return true;
}

RobotModel::VectorXd RobotModel::clampJointLimits(const VectorXd& q) const {
    VectorXd qc = q;
    for (int i = 0; i < nq_; ++i) {
        qc[i] = std::max(q_min_[i], std::min(q_max_[i], qc[i]));
    }
    return qc;
}

std::string RobotModel::getRobotName() const {
    if (!initialized_) {
        return "";
    }
    return model_.name;
}

} // namespace robot_infra
