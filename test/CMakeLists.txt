# Test executables and examples

# 查找Google Test库
find_package(GTest QUIET)

# 检查文件是否存在并添加可执行文件
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/comm_test.cpp)
    add_executable(comm_test comm_test.cpp)
    target_link_libraries(comm_test
        robot_infra
    )
endif()

if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/hw_sim_test.cpp)
    add_executable(hw_sim_test hw_sim_test.cpp)
    target_link_libraries(hw_sim_test
        robot_infra
    )
endif()

if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/toppra_fifo_demo.cpp)
    add_executable(toppra_fifo_demo toppra_fifo_demo.cpp)
    target_link_libraries(toppra_fifo_demo
        robot_infra
    )
endif()

# TOPPRA examples subdirectory
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/toppra)
    add_subdirectory(toppra)
endif()

# # 轨迹控制测试程序
# if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/unified_trajectory_test.cpp)
#     add_executable(unified_trajectory_test unified_trajectory_test.cpp)
#     target_link_libraries(unified_trajectory_test robot_infra)
#     message(STATUS "Added unified_trajectory_test")
# endif()

# if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/abc_def_sync_test.cpp)
#     add_executable(abc_def_sync_test abc_def_sync_test.cpp)
#     target_link_libraries(abc_def_sync_test robot_infra)
#     message(STATUS "Added abc_def_sync_test")
# endif()

# 自动发现并构建所有其他测试文件
file(GLOB TEST_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
foreach(test_src ${TEST_SOURCES})
    get_filename_component(test_name ${test_src} NAME_WE)
    # 避免重复定义已经明确定义的目标
    if(NOT test_name STREQUAL "comm_test" AND
       NOT test_name STREQUAL "hw_sim_test" AND
       NOT test_name STREQUAL "toppra_fifo_demo" AND
       NOT test_name STREQUAL "unified_trajectory_test")
        add_executable(${test_name} ${test_src})

        # 如果是Google Test测试文件且找到了GTest库，则链接GTest
        if(GTest_FOUND AND test_name MATCHES ".*_test$")
            target_link_libraries(${test_name}
                robot_infra
                GTest::GTest
                GTest::Main
            )
            message(STATUS "Added Google Test: ${test_name}")
        else()
            target_link_libraries(${test_name}
                robot_infra
            )
            message(STATUS "Added test: ${test_name}")
        endif()
    endif()
endforeach()

# 添加自定义目标用于运行轨迹测试
if(TARGET unified_trajectory_test)
    add_custom_target(run_unified_test
        COMMAND unified_trajectory_test ../urdf/robot.urdf
        DEPENDS unified_trajectory_test
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Running unified trajectory test"
    )
endif()