#include "comm/HWComm.h"
#include <iostream>
#include <signal.h>
#include <atomic>
#include <thread>
#include <chrono>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <cmath>

// std::atomic<bool> g_stop_flag(false);

// void signal_handler(int sig) {
//     std::cout << "\nInterrupt signal (" << sig << ") received." << std::endl;
//     g_stop_flag = true;
// }

int main() {
    std::cout << "=== Hardware Simulation ===" << std::endl;
    
    // // 设置信号处理
    // signal(SIGINT, signal_handler);
    // signal(SIGTERM, signal_handler);
    
    // 创建硬件模拟实例 (默认使用 ZMQ)
    HWComm hw(CommType::ZMQ);//ZMQ
    
    if (!hw.init()) {
        std::cerr << "Failed to initialize hardware simulation" << std::endl;
        return -1;
    }
    
    std::cout << "Communication type: " << (hw.get_connection_info().find("ZMQ") != std::string::npos ? "ZMQ" : "FIFO") << std::endl;
    std::cout << "Connection: " << hw.get_connection_info() << std::endl;
    
    // 设置实时数据接收回调，增加连续性验证，速度，加速度，todo 增加画图

    // [HWComm] Recv: pos=[149.536, 108.065, 0, 0, 0, 0] vel=[0, 0, 0, 0, 0, 0] dt=7424 tp=87285893527
    // [HWComm] Recv: pos=[0, 0, 0, 0, 0, 0] vel=[0, 0, 0, 0, 0, 0] dt=7440 tp=87285909541
    // [HWComm] Recv: pos=[149.536, 108.065, 0, 0, 0, 0] vel=[0, 0, 0, 0, 0, 0] dt=7456 tp=87285925597

    hw.recvRtData([](const ServoCommand& cmd) {
        static uint64_t last_timestamp_us = 0;
        static int recv_count = 0;
        static auto start_time = std::chrono::steady_clock::now();
        static std::vector<double> last_position(6, 0.0);
        static std::vector<double> last_velocity(6, 0.0);
        static bool first_recv = true;
        static int discontinuity_count = 0;

        recv_count++;
        auto current_time = std::chrono::steady_clock::now();

        // 计算接收间隔
        double interval_ms = 0.0;
        if (last_timestamp_us > 0) {
            interval_ms = (cmd.timestamp_us - last_timestamp_us) / 1000.0;
        }

        // 计算接收频率
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time);
        double avg_freq = (elapsed.count() > 0) ? (recv_count * 1000.0 / elapsed.count()) : 0.0;

        // 当前位置
        std::vector<double> current_position(6);
        for (int i = 0; i < 6; ++i) {
            current_position[i] = cmd.position[i];
        }

        // 连续性验证和速度/加速度计算
        if (!first_recv && interval_ms > 0) {
            double dt_s = interval_ms / 1000.0;  // 转换为秒

            // 计算速度 (mm/s 或 rad/s)
            std::vector<double> calculated_velocity(6);
            double max_position_jump = 0.0;

            for (int i = 0; i < 6; ++i) {
                calculated_velocity[i] = (current_position[i] - last_position[i]) / dt_s;
                double position_jump = std::abs(current_position[i] - last_position[i]);
                max_position_jump = std::max(max_position_jump, position_jump);
            }

            // 计算加速度 (mm/s² 或 rad/s²)
            std::vector<double> calculated_acceleration(6);
            for (int i = 0; i < 6; ++i) {
                calculated_acceleration[i] = (calculated_velocity[i] - last_velocity[i]) / dt_s;
            }

            // 检测不连续性 (位置跳跃超过阈值)
            double discontinuity_threshold = 50.0;  // 50mm或50度的跳跃认为是不连续
            bool is_discontinuous = max_position_jump > discontinuity_threshold;

            if (is_discontinuous) {
                discontinuity_count++;
                std::cout <<"*****************************************************************************************"<<std::endl;
                std::cout << "\n[DISCONTINUITY #" << discontinuity_count << "] Recv: [" << recv_count << "]: "
                          << "pos=[" << current_position[0] << ", " << current_position[1] << ", " << current_position[2] << "] "
                          << "jump=" << std::fixed << std::setprecision(2) << max_position_jump << "mm "
                          << "interval=" << interval_ms << "ms" << std::endl;
                std::cout <<"#########################################################################################"<<std::endl;
            }

            // // 定期输出详细信息
            // if (recv_count % 50 == 0) {
            //     std::cout << "Recv: [" << recv_count << "]: "
            //               << "pos=[" << std::fixed << std::setprecision(2)
            //               << current_position[0] << ", " << current_position[1] << ", " << current_position[2] << "] "
            //               << "vel=[" << calculated_velocity[0] << ", " << calculated_velocity[1] << ", " << calculated_velocity[2] << "] "
            //               << "acc=[" << std::setprecision(1)
            //               << calculated_acceleration[0] << ", " << calculated_acceleration[1] << ", " << calculated_acceleration[2] << "] "
            //               << "freq=" << avg_freq << "Hz "
            //               << "disc=" << discontinuity_count << std::endl;
            // }

            // 更新上次速度
            last_velocity = calculated_velocity;
        } else {
            first_recv = false;
        }

        // 更新状态
        last_position = current_position;
        last_timestamp_us = cmd.timestamp_us;
    });
    
    // 反馈通道演示
    std::cout << "\n[HW] Sending feedback to robot..." << std::endl;
    bool fb_sent = hw.sendFeedback("Test feedback from HW");
    std::cout << "[HW] Feedback sent: " << (fb_sent ? "OK" : "Failed") << std::endl;

    std::cout << "[HW] Receiving feedback from robot..." << std::endl;
    for (int i = 0; i < 50 ; ++i) {
        hw.recvFeedback([](const std::string& feedback) {
            std::cout << "[HW] Received feedback: " << feedback << std::endl;
        });
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    hw.run();

    std::cout << "Hardware simulation stopped." << std::endl;
    return 0;
} 