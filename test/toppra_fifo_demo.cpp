#include "../include/trajectory/ToppraInterpolator.hpp"
#include "../include/trajectory/RuckigInterpolator.hpp"
#include "industRob.h"
#include <vector>
#include <thread>
#include <chrono>
#include <iostream>
#include <Eigen/Dense>

using Vec3 = Eigen::Vector3d;

int main() {
    std::cout << "=== Toppra industRob Demo ===" <<123654 << std::endl;


    //初始化 industRob 通信
    industRob robot;
    robot.connect();
    if (!robot.isConnected()) {
        std::cerr << "Failed to connect SDK" << std::endl;
        return -1;
    }
    std::cout << "[Robot] Robot initialized "<< 1235 << std::endl;
    constexpr int DOF = 6;
    double dt = 0.016;
    int dt_ms = 1000*dt;
 
    ToppraInterpolator<DOF> toppra_interp(dt, "");
        
    // Set constraints
    MotionConstraints<DOF> constraints;
    constraints.max_velocity.setOnes() * 4.0;
    constraints.max_acceleration.setOnes() * 20.0;
    constraints.max_jerk.setOnes() * 50.0;
    toppra_interp.setConstraints(constraints);
    
    // Create waypoints
    std::vector<MotionState<DOF>> waypoints;

    // 简化的路径点添加
    waypoints.push_back(MotionState<DOF>((Eigen::VectorXd(DOF) << 0.0, 0.0, 0.0, 0.0, 0.0, 0.0).finished()));
    waypoints.push_back(MotionState<DOF>((Eigen::VectorXd(DOF) << 0.0, 0.0, 0.03, 0.0, 0.0, 0.0).finished()));
    waypoints.push_back(MotionState<DOF>((Eigen::VectorXd(DOF) << 0.4, 0.0, 0.04, 0.0, 0.0, 0.0).finished()));
    waypoints.push_back(MotionState<DOF>((Eigen::VectorXd(DOF) << 0.4, 0.0, 0.00, 0.0, 0.0, 0.0).finished()));
    waypoints.push_back(MotionState<DOF>((Eigen::VectorXd(DOF) << 0.0, 0.1, 0.03, 0.0, 0.0, 0.0).finished()));
    waypoints.push_back(MotionState<DOF>((Eigen::VectorXd(DOF) << 0.0, 0.0, 0.01, 0.0, 0.0, 0.0).finished()));
    waypoints.push_back(MotionState<DOF>((Eigen::VectorXd(DOF) << 0.0, 0.0, 0.0, 0.0, 0.0, 0.0).finished()));
    // Test offline computation
    bool success = toppra_interp.computeOffline(waypoints);
    std::cout << "ToppraInterpolator offline computation: " 
                << (success ? "SUCCESS" : "FAILED") << std::endl;
    if (!success) {
        std::cout << "Error: " << toppra_interp.getLastError() << std::endl;
    }

    // Get trajectory buffer and sample at regular intervals
    const auto& trajectory_buffer = toppra_interp.getTrajectoryBuffer();

    double duration = toppra_interp.getDuration();
    int num_samples = static_cast<int>(duration / dt) + 1;

    // Set output precision
    std::cout << std::fixed << std::setprecision(4);
    std::cout << "Buffer size: " << trajectory_buffer.size() << " time" << trajectory_buffer.getDuration()  << std::endl;

    
    // === PT动态模式正弦轨迹生成（Eigen 版本） ===========================================================================
    const double A_PT = 0.0006;      // 幅值（单位：米/弧度）
    const double T_PT = 1.0;      // 周期（秒）
    const double DELTA_PT = dt; // 采样周期（秒）
    const int LOOP_PT = 50;        // 循环次数
    const double PI = 3.141592653589793;
    const double omega = 2 * PI / T_PT;
    const int N = static_cast<int>(T_PT / DELTA_PT) * LOOP_PT;

    std::vector<Eigen::Matrix<double, 6, 1>> sin_pos_traj, sin_vel_traj;
    std::vector<double> sin_time_traj;
    Eigen::Matrix<double, 6, 1> sin_pos = Eigen::Matrix<double, 6, 1>::Zero();
    Eigen::Matrix<double, 6, 1> sin_vel = Eigen::Matrix<double, 6, 1>::Zero();
    Eigen::Matrix<double, 6, 1> sin_velPre = Eigen::Matrix<double, 6, 1>::Zero();
    double t = 0.0;
    int loop = 1;
    while (loop <= LOOP_PT) {
        t += DELTA_PT;
        for (int j = 0; j < 6; ++j) {
            sin_vel(j) = A_PT * std::sin(omega * t);
            sin_pos(j) += 1000*(sin_vel(j) + sin_velPre(j)) * DELTA_PT / 2.0; // 梯形积分
            sin_velPre(j) = sin_vel(j);
        }
        //std::cout << "t: " << t << "  sin_pos: " << sin_pos[0] << std::endl;

        sin_pos_traj.push_back(sin_pos);
        sin_vel_traj.push_back(sin_vel);
        sin_time_traj.push_back(t);
        if (t >= loop * T_PT) {
            sin_pos.setZero();
            sin_velPre.setZero();
            t = loop * T_PT;
            ++loop;
        }
    }
    //robot.servoMode(1, 16, 1.0, 20);
    
    //========== 选择发送哪种轨迹 =============
    // 1. 发送正弦轨迹（6轴）
        //========== 选择发送哪种轨迹 =============
    // 1. 发送正弦轨迹（6轴） - 修改为精确延时
    auto next_cycle_time = std::chrono::steady_clock::now();
    const auto cycle_duration = std::chrono::milliseconds(dt_ms);

    double dt_sec = dt_ms / 1000.0;  // 转换为秒
    double lookahead_time = 0.1;     // 前瞻时间
    double gain = 0.5;               // 增益
    
    // for (size_t i = 0; i < sin_pos_traj.size(); ++i) {
    //     // 记录循环开始时间
    //     auto cycle_start = std::chrono::steady_clock::now();
        
    //     // 计算下一个周期的目标时间
    //     next_cycle_time += cycle_duration;
        
    //     // 使用servoL接口发送关节位置命令
    //     std::vector<double> joint_positions(6, 0.0);
    //     joint_positions[0] = sin_pos_traj[i](0);  // 第一个关节使用正弦轨迹
    //     // 其他关节保持为0
    
    //     robot.servoL(joint_positions, i * dt, lookahead_time, gain);
        
    //     // 精确延时到下一个周期
    //     auto now = std::chrono::steady_clock::now();
    //     if (now < next_cycle_time) {
    //         std::this_thread::sleep_until(next_cycle_time);
    //     } else {
    //         // 如果已经超时，重新同步
    //         auto cycles_behind = std::chrono::duration_cast<std::chrono::milliseconds>(now - next_cycle_time).count() / dt_ms + 1;
    //         next_cycle_time += cycle_duration * cycles_behind;
    //         std::cout << "Warning: Sin trajectory cycle " << i << " overrun by " << cycles_behind << " cycles" << std::endl;
    //     }
        
    //     // 可选：打印实际周期时间
    //     if (i % 50 == 0) {  // 每50个周期打印一次
    //         auto actual_cycle_time = std::chrono::duration_cast<std::chrono::microseconds>(
    //             std::chrono::steady_clock::now() - cycle_start).count();
    //         std::cout << "Sin cycle " << i << ": actual=" << actual_cycle_time << "us, target="
    //                   << dt_ms * 1000 << "us" << std::endl;
    //     }
    // }
    // getchar();

    //2. 发送Toppra插补轨迹（前3轴，后三轴补零）


    // 精确延时控制

    // for (int i = 0; i < num_samples; ++i) {
    //     // 记录循环开始时间
    //     auto cycle_start = std::chrono::steady_clock::now();

    //     // 计算下一个周期的目标时间
    //     next_cycle_time += cycle_duration;

    //     // 使用servoL接口发送Toppra轨迹
    //     std::vector<double> joint_positions(6, 0.0);

    //     double t = std::min(i * dt, duration);
    //     TrajectoryState<DOF> state = trajectory_buffer.getStateAtTime(t);
    //     static int cnt = 0;
    //     cnt++;
    //     std::copy(state.position.data(), state.position.data() + state.position.size(), joint_positions.begin());
    //     robot.servoL(joint_positions, dt_sec*cnt, lookahead_time, gain);

    //     // 精确延时到下一个周期
    //     auto now = std::chrono::steady_clock::now();
    //     if (now < next_cycle_time) {
    //         std::this_thread::sleep_until(next_cycle_time);
    //     } else {
    //         // 如果已经超时，重新同步
    //         auto cycles_behind = std::chrono::duration_cast<std::chrono::milliseconds>(now - next_cycle_time).count() / dt_ms + 1;
    //         next_cycle_time += cycle_duration * cycles_behind;
    //         std::cout << "Warning: Cycle " << i << " overrun by " << cycles_behind << " cycles" << std::endl;
    //     }

    //     // 可选：打印实际周期时间
    //     if (i % 50 == 0) {  // 每50个周期打印一次
    //         auto actual_cycle_time = std::chrono::duration_cast<std::chrono::microseconds>(
    //             std::chrono::steady_clock::now() - cycle_start).count();
    //         std::cout << "Cycle " << i << ": actual=" << actual_cycle_time << "us, target="
    //                   << dt_ms * 1000 << "us" << std::endl;
    //     }
    // }


    static int cnt = 0;
    double last_pos = 0.0;
    while (1)
    {
        // 记录循环开始时间
        auto cycle_start = std::chrono::steady_clock::now();
        // 计算下一个周期的目标时间
        next_cycle_time += cycle_duration;

        // 使用servoL接口发送关节位置命令
        std::vector<double> joint_positions(6, 0.0);
        if(cnt%10 == 0)
        {
            joint_positions[0] = 0.0002 * cnt;  // 第一个关节位置
            last_pos = joint_positions[0]; // 更新上一个位置
        }else
        {
            joint_positions[0] = last_pos; // 保持上一个位置
        }
        
        // 其他关节保持为0
        double lookahead_time = 0.1; // 前瞻时间
        double gain = 0.5;           // 增益

        robot.servoL(joint_positions, dt_sec*cnt, lookahead_time, gain);

        std::cout << "[Robot] ServoL command sent: "
                  << "Position[0]: " << joint_positions[0] << ", "
                  << "dt: " << dt_sec << ", "
                  << "Count: " << cnt++ << std::endl;

        // 精确延时到下一个周期
        auto now = std::chrono::steady_clock::now();
        if (now < next_cycle_time) {
            std::this_thread::sleep_until(next_cycle_time);
        } else {
            // 如果已经超时，重新同步并警告
            auto overrun_ms = std::chrono::duration_cast<std::chrono::milliseconds>(now - next_cycle_time).count();
            next_cycle_time = now;  // 重新同步
            std::cout << "[Warning] Cycle overrun by " << overrun_ms << "ms" << std::endl;
        }
        // 每10个周期打印一次实际周期时间
        if (cnt % 10 == 0) {
            auto actual_cycle_time = std::chrono::duration_cast<std::chrono::microseconds>(
                std::chrono::steady_clock::now() - cycle_start).count();
            std::cout << "[Timing] Cycle " << cnt << ": actual=" << actual_cycle_time
                      << "us, target=" << dt_sec * 1000000 << "us" << std::endl;
        }
    }

    std::cout << "[Robot] All servo commands sent." << std::endl;
    return 0;
} 










