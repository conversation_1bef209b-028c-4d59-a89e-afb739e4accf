#include "industRob.h"
#include <iostream>
#include <chrono>
#include <thread>

void print_robot_state(const RobotFullState& state) {
    //std::cout<<" recv: "<<state.joint_position[0]<<std::endl;
    // std::cout << "[APP] Robot State: ts=" << state.timestamp_us
    //           << " joint_pos=[";
    // for (auto v : state.joint_position) std::cout << v << " ";
    // std::cout << "] cart_pos=[";
    // for (auto v : state.cartesian_position) std::cout << v << " ";
    // std::cout << "] servo_status=" << state.servo_status << std::endl;
}

int main() {
    std::cout << "=== Robot SDK Demo (New API) ===" << std::endl;
    industRob sdk(CommType::ZMQ);
    sdk.connect();
    if (!sdk.isConnected()) {
        std::cerr << "Failed to connect SDK" << std::endl;
        return -1;
    }
    std::cout << "SDK connected." << std::endl;

    // 注册统一状态回调
    sdk.registerStateCallback(print_robot_state);

    std::cout << "\n1. 初始化机器人..." << std::endl;
    sdk.init();
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    std::cout << "\n2. 复位..." << std::endl;
    sdk.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));

    std::cout << "\n3. 发送servoj运动指令..." << std::endl;
    std::vector<double> joints = {10, 15, 20, 25, 30, 35};
    double dt = 0.001;
    for (size_t i = 0; i < 5; i++)
    {
        sdk.servoj(joints, dt, 1.0, 0.0);
        std::this_thread::sleep_for(std::chrono::milliseconds(int(dt*1000)));
    }
    

    
    // std::cout << "\n4. 发送笛卡尔运动指令..." << std::endl;
    // std::vector<double> pose = {100, 200, 300, 0, 0, 0};
    // sdk.movL(pose, 1.0, 1.0, 0.0);
    // std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // std::cout << "\n5. 发送多点轨迹..." << std::endl;
    // std::vector<std::vector<double>> path = {
    //     {100, 200, 300, 0, 0, 0},
    //     {110, 210, 310, 0, 0, 0},
    //     {120, 220, 320, 0, 0, 0}
    // };
    // sdk.movL(path, 1.0, 1.0, 0.0);
    // std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // std::cout << "\n6. 等待断开连接..." << std::endl;

    sdk.disconnect();
    std::cout << "Demo completed!" << std::endl;
    return 0;
} 