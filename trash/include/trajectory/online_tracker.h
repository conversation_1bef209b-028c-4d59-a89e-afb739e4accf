#ifndef ONLINE_TRACKER_H
#define ONLINE_TRACKER_H

#include "RuckigInterpolator.h"
#include "TrajectoryTypes.h"
#include <iostream>
#include <chrono>
#include <Eigen/Dense>
#include <atomic>
#include <mutex>

namespace trajectory {

class OnlineTracker {
private:
    RuckigInterpolator<6> interpolator_;  // 临时固定为6DOF

    // 当前状态
    TrajectoryState current_state_;
    TrajectoryState target_state_;
    mutable std::mutex state_mutex_;

    // 控制参数
    Eigen::VectorXd max_velocity_;
    Eigen::VectorXd max_acceleration_;
    Eigen::VectorXd max_jerk_;

    // 控制参数引用
    ControlParameters control_params_;

    // 状态标志
    std::atomic<bool> has_new_target_;
    std::atomic<bool> target_reached_;

    // 轨迹缓存
    std::vector<TrajectoryState> trajectory_cache_;
    size_t cache_index_;
    double cache_duration_;

public:
    explicit OnlineTracker(const std::string& urdf_path, const ControlParameters& params)
        : interpolator_(params.control_period, urdf_path)
        , current_state_(params.dof)
        , target_state_(params.dof)
        , control_params_(params)
        , max_velocity_(params.max_velocity)
        , max_acceleration_(params.max_acceleration)
        , max_jerk_(params.max_jerk)
        , has_new_target_(false)
        , target_reached_(true)
        , cache_index_(0)
        , cache_duration_(0.0) {

        // 配置插补器约束
        interpolator_.setConstraint(max_velocity_, max_acceleration_, max_jerk_);
    }

    ~OnlineTracker() = default;
    
    // 主要接口方法
public:
    // 设置当前状态
    void setCurrentState(const TrajectoryState& state);

    // 设置目标状态
    void setTarget(const Eigen::VectorXd& target_position,
                   const Eigen::VectorXd& target_velocity = Eigen::VectorXd());

    // 获取下一个状态
    TrajectoryState getNextState(double dt);

    // 状态查询
    bool isTargetReached() const { return target_reached_.load(); }
    bool hasNewTarget() const { return has_new_target_.load(); }
    TrajectoryState getCurrentState() const;
    TrajectoryState getTargetState() const;

    // 约束设置
    void setConstraints(const Eigen::VectorXd& max_vel,
                       const Eigen::VectorXd& max_acc,
                       const Eigen::VectorXd& max_jerk);

    // 重置状态
    void reset();

private:
    // 内部方法
    bool planTrajectory();
    void updateTrajectoryCache();
    bool isNearTarget(double position_tolerance = 1e-3, double velocity_tolerance = 1e-2) const;
};

} // namespace trajectory

#endif // ONLINE_TRACKER_H