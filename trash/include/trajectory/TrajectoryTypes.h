#ifndef TRAJECTORY_TYPES_H
#define TRAJECTORY_TYPES_H

#include <Eigen/Dense>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <string>
#include <iostream>
#include <type_traits>
#include <chrono>
#include <functional>

namespace trajectory {

// 轨迹控制模式
enum class TrajectoryMode {
    OFFLINE,              // 离线轨迹执行模式（使用TOPPRA）
    ONLINE,               // 在线跟踪模式（使用Ruckig）
    TRANSITION_TO_ONLINE, // 从离线切换到在线的过渡状态
    TRANSITION_TO_OFFLINE,// 从在线切换到离线的过渡状态
    IDLE                  // 空闲状态
};

// 事件类型定义
enum class TrajectoryEvent {
    MODE_SWITCH_REQUEST,
    TRAJECTORY_UPDATE,
    STATE_TRANSITION,
    ERROR_OCCURRED,
    INTERPOLATOR_READY,
    BUFFER_SWAP
};

// 模板化向量类型选择器
template<int DOF>
struct VectorTypeSelector {
    using type = std::conditional_t<DOF == Eigen::Dynamic,
                                   Eigen::VectorXd,
                                   Eigen::Matrix<double, DOF, 1>>;
    using matrix_type = std::conditional_t<DOF == Eigen::Dynamic,
                                          Eigen::MatrixXd,
                                          Eigen::Matrix<double, DOF, Eigen::Dynamic>>;
};

// 便利别名
template<int DOF>
using VectorType = typename VectorTypeSelector<DOF>::type;

template<int DOF>
using MatrixType = typename VectorTypeSelector<DOF>::matrix_type;

// 模板化轨迹状态
template<int DOF = Eigen::Dynamic>
class TrajectoryState {
public:
    using VectorType = trajectory::VectorType<DOF>;

    VectorType position;     // 关节位置
    VectorType velocity;     // 关节速度
    VectorType acceleration; // 关节加速度
    double timestamp;        // 时间戳
    bool valid;              // 状态是否有效

    // 默认构造函数
    explicit TrajectoryState(int dof = (DOF == Eigen::Dynamic ? 6 : DOF))
        : timestamp(0.0), valid(false) {
        if constexpr (DOF == Eigen::Dynamic) {
            position = VectorType::Zero(dof);
            velocity = VectorType::Zero(dof);
            acceleration = VectorType::Zero(dof);
        } else {
            position = VectorType::Zero();
            velocity = VectorType::Zero();
            acceleration = VectorType::Zero();
        }
    }

    // 参数构造函数
    TrajectoryState(const VectorType& pos,
                   const VectorType& vel,
                   const VectorType& acc,
                   double t = 0.0)
        : position(pos), velocity(vel), acceleration(acc), timestamp(t), valid(true) {}

    // 拷贝构造函数
    TrajectoryState(const TrajectoryState& other) = default;

    // 赋值操作符
    TrajectoryState& operator=(const TrajectoryState& other) = default;

    // 移动构造和赋值
    TrajectoryState(TrajectoryState&& other) noexcept = default;
    TrajectoryState& operator=(TrajectoryState&& other) noexcept = default;

    // 获取自由度
    int getDOF() const {
        return static_cast<int>(position.size());
    }

    // 检查状态是否接近目标
    bool isNear(const TrajectoryState& target, double pos_tol = 1e-3, double vel_tol = 1e-2) const {
        if (!valid || !target.valid) return false;

        double pos_error = (position - target.position).norm();
        double vel_error = (velocity - target.velocity).norm();

        return pos_error < pos_tol && vel_error < vel_tol;
    }

    // 状态插值
    TrajectoryState interpolate(const TrajectoryState& other, double alpha) const {
        if (!valid || !other.valid) return TrajectoryState(getDOF());

        TrajectoryState result(getDOF());
        result.position = (1.0 - alpha) * position + alpha * other.position;
        result.velocity = (1.0 - alpha) * velocity + alpha * other.velocity;
        result.acceleration = (1.0 - alpha) * acceleration + alpha * other.acceleration;
        result.timestamp = (1.0 - alpha) * timestamp + alpha * other.timestamp;
        result.valid = true;

        return result;
    }

    // 重置状态
    void reset() {
        position.setZero();
        velocity.setZero();
        acceleration.setZero();
        timestamp = 0.0;
        valid = false;
    }
};

// 动态DOF的特化版本别名
using DynamicTrajectoryState = TrajectoryState<Eigen::Dynamic>;

// 模板化轨迹缓冲区
template<int DOF = Eigen::Dynamic>
class TrajectoryBuffer {
public:
    using StateType = TrajectoryState<DOF>;
    using StateVector = std::vector<StateType>;

    StateVector states;           // 轨迹状态序列
    std::vector<double> timestamps; // 时间戳序列
    double duration;              // 总持续时间
    size_t current_index;         // 当前索引
    bool is_valid;                // 缓冲区是否有效
    bool is_looping;              // 是否循环执行
    int dof;                      // 自由度

    explicit TrajectoryBuffer(int degrees_of_freedom = (DOF == Eigen::Dynamic ? 6 : DOF))
        : duration(0.0), current_index(0), is_valid(false), is_looping(false), dof(degrees_of_freedom) {}

    void clear() {
        states.clear();
        timestamps.clear();
        duration = 0.0;
        current_index = 0;
        is_valid = false;
        is_looping = false;
    }

    bool isEmpty() const {
        return states.empty() || !is_valid;
    }

    // 获取当前状态
    StateType getCurrentState() const {
        if (isEmpty() || current_index >= states.size()) {
            return StateType(dof); // 返回无效状态
        }
        return states[current_index];
    }

    // 获取指定时间的状态（线性插值）
    StateType getStateAtTime(double t) const {
        if (isEmpty()) return StateType(dof);

        // 处理循环模式
        if (is_looping && duration > 0) {
            t = std::fmod(t, duration);
            if (t < 0) t += duration;
        }

        // 边界检查
        if (t <= 0) return states.front();
        if (t >= duration) return states.back();

        // 二分查找时间区间
        auto it = std::lower_bound(timestamps.begin(), timestamps.end(), t);
        if (it == timestamps.end()) return states.back();

        size_t idx = std::distance(timestamps.begin(), it);
        if (idx == 0) return states[0];

        // 线性插值
        double t1 = timestamps[idx - 1];
        double t2 = timestamps[idx];
        double alpha = (t - t1) / (t2 - t1);

        return states[idx - 1].interpolate(states[idx], alpha);
    }

    // 更新当前索引
    void updateIndex(double current_time) {
        if (isEmpty()) return;

        // 处理循环模式
        if (is_looping && duration > 0) {
            current_time = std::fmod(current_time, duration);
            if (current_time < 0) current_time += duration;
        }

        // 查找对应的索引
        auto it = std::lower_bound(timestamps.begin(), timestamps.end(), current_time);
        if (it != timestamps.end()) {
            current_index = std::distance(timestamps.begin(), it);
            current_index = std::min(current_index, states.size() - 1);
        } else {
            current_index = states.size() - 1;
        }
    }

    // 检查是否到达末尾
    bool isFinished(double current_time) const {
        if (isEmpty() || is_looping) return false;
        return current_time >= duration;
    }

    // 添加状态到缓冲区
    void addState(const StateType& state, double timestamp) {
        states.push_back(state);
        timestamps.push_back(timestamp);
        duration = std::max(duration, timestamp);
        is_valid = true;
    }

    // 预留空间
    void reserve(size_t capacity) {
        states.reserve(capacity);
        timestamps.reserve(capacity);
    }

    // 获取大小
    size_t size() const {
        return states.size();
    }
};

// 动态DOF的特化版本别名
using DynamicTrajectoryBuffer = TrajectoryBuffer<Eigen::Dynamic>;

// 模板化模式切换事件
template<int DOF = Eigen::Dynamic>
struct ModeTransitionEvent {
    using StateType = TrajectoryState<DOF>;

    TrajectoryMode from_mode;
    TrajectoryMode to_mode;
    StateType transition_start_state;
    double transition_duration;
    bool requires_smooth_transition;
    std::chrono::steady_clock::time_point timestamp;

    ModeTransitionEvent(TrajectoryMode from, TrajectoryMode to,
                       const StateType& start_state,
                       double duration = 0.5,
                       bool smooth = true)
        : from_mode(from), to_mode(to), transition_start_state(start_state)
        , transition_duration(duration), requires_smooth_transition(smooth)
        , timestamp(std::chrono::steady_clock::now()) {}
};

// 通用事件基类
class IEvent {
public:
    virtual ~IEvent() = default;
    virtual TrajectoryEvent getType() const = 0;
    virtual std::chrono::steady_clock::time_point getTimestamp() const = 0;
};

// 模板化事件类
template<TrajectoryEvent EventType, typename EventData>
class Event : public IEvent {
public:
    EventData data;
    std::chrono::steady_clock::time_point timestamp;

    explicit Event(EventData&& event_data)
        : data(std::forward<EventData>(event_data))
        , timestamp(std::chrono::steady_clock::now()) {}

    TrajectoryEvent getType() const override { return EventType; }
    std::chrono::steady_clock::time_point getTimestamp() const override { return timestamp; }
};

// 模板化线程安全的轨迹状态容器
template<int DOF = Eigen::Dynamic>
class ThreadSafeTrajectoryState {
public:
    using StateType = TrajectoryState<DOF>;
    using VectorType = trajectory::VectorType<DOF>;

private:
    mutable std::mutex mutex_;
    StateType state_;

public:
    explicit ThreadSafeTrajectoryState(int dof = (DOF == Eigen::Dynamic ? 6 : DOF))
        : state_(dof) {}

    void set(const StateType& state) {
        std::lock_guard<std::mutex> lock(mutex_);
        state_ = state;
    }

    StateType get() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return state_;
    }

    void update(const VectorType& pos, const VectorType& vel,
               const VectorType& acc, double timestamp) {
        std::lock_guard<std::mutex> lock(mutex_);
        state_.position = pos;
        state_.velocity = vel;
        state_.acceleration = acc;
        state_.timestamp = timestamp;
        state_.valid = true;
    }

    // 原子性地获取特定字段
    VectorType getPosition() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return state_.position;
    }

    VectorType getVelocity() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return state_.velocity;
    }

    double getTimestamp() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return state_.timestamp;
    }

    bool isValid() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return state_.valid;
    }
};

// 模板化控制参数配置类
template<int DOF = Eigen::Dynamic>
class ControlParameters {
public:
    using VectorType = trajectory::VectorType<DOF>;

    // 基本控制参数
    double control_frequency = 1000.0;  // 1kHz
    double control_period = 1.0 / control_frequency;
    int dof = (DOF == Eigen::Dynamic ? 6 : DOF);  // 自由度

    // 运动约束
    VectorType max_velocity;
    VectorType max_acceleration;
    VectorType max_jerk;

    // 过渡参数
    double transition_duration = 0.5;
    double position_tolerance = 1e-3;
    double velocity_tolerance = 1e-2;

    // 在线跟踪参数
    double lookahead_time = 0.1;
    double servo_gain = 0.5;

    // 构造函数 - 提供默认值
    explicit ControlParameters(int degrees_of_freedom = (DOF == Eigen::Dynamic ? 6 : DOF))
        : dof(degrees_of_freedom) {
        updateControlPeriod();
        initializeConstraints();
    }

    // 设置控制频率并自动更新周期
    void setControlFrequency(double freq) {
        control_frequency = freq;
        updateControlPeriod();
    }

    // 设置自由度并重新初始化约束（仅对动态DOF有效）
    template<int D = DOF>
    typename std::enable_if_t<D == Eigen::Dynamic, void>
    setDOF(int degrees_of_freedom) {
        dof = degrees_of_freedom;
        initializeConstraints();
    }

    // 设置运动约束
    void setConstraints(const VectorType& max_vel,
                       const VectorType& max_acc,
                       const VectorType& max_jerk_param) {
        if constexpr (DOF == Eigen::Dynamic) {
            if (max_vel.size() != dof || max_acc.size() != dof || max_jerk_param.size() != dof) {
                throw std::invalid_argument("Constraint vector size must match DOF");
            }
        }
        max_velocity = max_vel;
        max_acceleration = max_acc;
        max_jerk = max_jerk_param;
    }

    // 预设配置
    static ControlParameters createConservativeConfig(int degrees_of_freedom = (DOF == Eigen::Dynamic ? 6 : DOF)) {
        ControlParameters params(degrees_of_freedom);
        if constexpr (DOF == Eigen::Dynamic) {
            params.max_velocity = VectorType::Constant(degrees_of_freedom, 1.0);
            params.max_acceleration = VectorType::Constant(degrees_of_freedom, 3.0);
            params.max_jerk = VectorType::Constant(degrees_of_freedom, 10.0);
        } else {
            params.max_velocity = VectorType::Constant(1.0);
            params.max_acceleration = VectorType::Constant(3.0);
            params.max_jerk = VectorType::Constant(10.0);
        }
        params.transition_duration = 1.0;
        return params;
    }

    static ControlParameters createAggressiveConfig(int degrees_of_freedom = (DOF == Eigen::Dynamic ? 6 : DOF)) {
        ControlParameters params(degrees_of_freedom);
        if constexpr (DOF == Eigen::Dynamic) {
            params.max_velocity = VectorType::Constant(degrees_of_freedom, 3.0);
            params.max_acceleration = VectorType::Constant(degrees_of_freedom, 15.0);
            params.max_jerk = VectorType::Constant(degrees_of_freedom, 50.0);
        } else {
            params.max_velocity = VectorType::Constant(3.0);
            params.max_acceleration = VectorType::Constant(15.0);
            params.max_jerk = VectorType::Constant(50.0);
        }
        params.transition_duration = 0.2;
        return params;
    }

    static ControlParameters createTestConfig(int degrees_of_freedom = (DOF == Eigen::Dynamic ? 6 : DOF)) {
        ControlParameters params(degrees_of_freedom);
        params.control_frequency = 100.0;  // 降低频率用于测试
        params.updateControlPeriod();
        if constexpr (DOF == Eigen::Dynamic) {
            params.max_velocity = VectorType::Constant(degrees_of_freedom, 0.5);
            params.max_acceleration = VectorType::Constant(degrees_of_freedom, 2.0);
            params.max_jerk = VectorType::Constant(degrees_of_freedom, 8.0);
        } else {
            params.max_velocity = VectorType::Constant(0.5);
            params.max_acceleration = VectorType::Constant(2.0);
            params.max_jerk = VectorType::Constant(8.0);
        }
        return params;
    }

    // 验证参数有效性
    bool validate() const {
        bool size_valid = true;
        if constexpr (DOF == Eigen::Dynamic) {
            size_valid = max_velocity.size() == dof &&
                        max_acceleration.size() == dof &&
                        max_jerk.size() == dof;
        }

        return control_frequency > 0 &&
               dof > 0 &&
               size_valid &&
               (max_velocity.array() > 0).all() &&
               (max_acceleration.array() > 0).all() &&
               (max_jerk.array() > 0).all();
    }

    // 打印配置信息
    void print() const {
        std::cout << "Control Parameters:" << std::endl;
        std::cout << "  DOF: " << dof << std::endl;
        std::cout << "  Control Frequency: " << control_frequency << " Hz" << std::endl;
        std::cout << "  Control Period: " << control_period << " s" << std::endl;
        std::cout << "  Max Velocity: " << max_velocity.transpose() << std::endl;
        std::cout << "  Max Acceleration: " << max_acceleration.transpose() << std::endl;
        std::cout << "  Max Jerk: " << max_jerk.transpose() << std::endl;
        std::cout << "  Transition Duration: " << transition_duration << " s" << std::endl;
    }

private:
    void updateControlPeriod() {
        control_period = 1.0 / control_frequency;
    }

    void initializeConstraints() {
        if constexpr (DOF == Eigen::Dynamic) {
            max_velocity = VectorType::Constant(dof, 2.0);
            max_acceleration = VectorType::Constant(dof, 10.0);
            max_jerk = VectorType::Constant(dof, 40.0);
        } else {
            max_velocity = VectorType::Constant(2.0);
            max_acceleration = VectorType::Constant(10.0);
            max_jerk = VectorType::Constant(40.0);
        }
    }
};

// 常用的特化版本别名
using DynamicControlParameters = ControlParameters<Eigen::Dynamic>;
using ControlParameters6DOF = ControlParameters<6>;
using ControlParameters7DOF = ControlParameters<7>;

} // namespace trajectory

#endif // TRAJECTORY_TYPES_H
