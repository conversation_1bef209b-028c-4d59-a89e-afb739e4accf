#ifndef INTERPOLATOR_INTERFACE_H
#define INTERPOLATOR_INTERFACE_H

#include "TrajectoryTypes.h"
#include <memory>
#include <vector>
#include <future>

namespace trajectory {

// 插补器类型枚举
enum class InterpolatorType {
    ONLINE,   // 在线插补器（如Ruckig）
    OFFLINE   // 离线插补器（如TOPPRA）
};

// 插补器状态
enum class InterpolatorState {
    IDLE,
    COMPUTING,
    READY,
    ERROR
};

// 插补结果
template<int DOF = Eigen::Dynamic>
struct InterpolationResult {
    using StateType = TrajectoryState<DOF>;
    
    bool success;
    std::string error_message;
    StateType current_state;
    double computation_time_ms;
    
    InterpolationResult() : success(false), computation_time_ms(0.0) {}
    
    explicit InterpolationResult(const StateType& state) 
        : success(true), current_state(state), computation_time_ms(0.0) {}
};

// 统一插补器接口基类
template<int DOF = Eigen::Dynamic>
class ITrajectoryInterpolator {
public:
    using StateType = TrajectoryState<DOF>;
    using VectorType = trajectory::VectorType<DOF>;
    using BufferType = TrajectoryBuffer<DOF>;
    using ParametersType = ControlParameters<DOF>;
    using ResultType = InterpolationResult<DOF>;

    virtual ~ITrajectoryInterpolator() = default;

    // 基本信息
    virtual InterpolatorType getType() const = 0;
    virtual InterpolatorState getState() const = 0;
    virtual std::string getName() const = 0;
    virtual int getDOF() const = 0;

    // 初始化和配置
    virtual bool initialize(const ParametersType& params) = 0;
    virtual void setConstraints(const VectorType& max_vel,
                               const VectorType& max_acc,
                               const VectorType& max_jerk) = 0;

    // 在线插补接口（实时计算）
    virtual ResultType computeOnline(const StateType& current_state,
                                   const StateType& target_state,
                                   double time_step) = 0;

    // 离线插补接口（批量计算）
    virtual bool computeOffline(const std::vector<VectorType>& waypoints,
                               BufferType& output_buffer) = 0;

    // 异步计算接口
    virtual std::future<bool> computeOfflineAsync(const std::vector<VectorType>& waypoints,
                                                 BufferType& output_buffer) = 0;

    // 状态查询
    virtual StateType getCurrentState() const = 0;
    virtual bool isReady() const = 0;
    virtual bool hasError() const = 0;
    virtual std::string getLastError() const = 0;

    // 重置和清理
    virtual void reset() = 0;
    virtual void shutdown() = 0;

    // 性能统计
    virtual double getAverageComputationTime() const = 0;
    virtual size_t getTotalComputations() const = 0;
};

// CRTP基类，提供通用实现
template<typename Derived, int DOF = Eigen::Dynamic>
class TrajectoryInterpolatorBase : public ITrajectoryInterpolator<DOF> {
public:
    using StateType = TrajectoryState<DOF>;
    using VectorType = trajectory::VectorType<DOF>;
    using BufferType = TrajectoryBuffer<DOF>;
    using ParametersType = ControlParameters<DOF>;
    using ResultType = InterpolationResult<DOF>;

protected:
    InterpolatorState state_;
    ParametersType parameters_;
    StateType current_state_;
    std::string last_error_;
    
    // 性能统计
    std::atomic<size_t> total_computations_;
    std::atomic<double> total_computation_time_;
    mutable std::mutex state_mutex_;

public:
    TrajectoryInterpolatorBase() 
        : state_(InterpolatorState::IDLE)
        , current_state_(DOF == Eigen::Dynamic ? 6 : DOF)
        , total_computations_(0)
        , total_computation_time_(0.0) {}

    // 基本信息实现
    InterpolatorState getState() const override {
        std::lock_guard<std::mutex> lock(state_mutex_);
        return state_;
    }

    int getDOF() const override {
        return parameters_.dof;
    }

    // 状态查询实现
    StateType getCurrentState() const override {
        std::lock_guard<std::mutex> lock(state_mutex_);
        return current_state_;
    }

    bool isReady() const override {
        return getState() == InterpolatorState::READY;
    }

    bool hasError() const override {
        return getState() == InterpolatorState::ERROR;
    }

    std::string getLastError() const override {
        std::lock_guard<std::mutex> lock(state_mutex_);
        return last_error_;
    }

    // 性能统计实现
    double getAverageComputationTime() const override {
        size_t count = total_computations_.load();
        if (count == 0) return 0.0;
        return total_computation_time_.load() / count;
    }

    size_t getTotalComputations() const override {
        return total_computations_.load();
    }

    // 重置实现
    void reset() override {
        std::lock_guard<std::mutex> lock(state_mutex_);
        state_ = InterpolatorState::IDLE;
        current_state_.reset();
        last_error_.clear();
        total_computations_ = 0;
        total_computation_time_ = 0.0;
    }

protected:
    // 辅助方法
    void setState(InterpolatorState new_state) {
        std::lock_guard<std::mutex> lock(state_mutex_);
        state_ = new_state;
    }

    void setError(const std::string& error_msg) {
        std::lock_guard<std::mutex> lock(state_mutex_);
        state_ = InterpolatorState::ERROR;
        last_error_ = error_msg;
    }

    void updateStatistics(double computation_time_ms) {
        total_computations_++;
        total_computation_time_ += computation_time_ms;
    }

    // CRTP接口 - 子类必须实现
    Derived& derived() { return static_cast<Derived&>(*this); }
    const Derived& derived() const { return static_cast<const Derived&>(*this); }
};

// 工厂函数声明
template<int DOF = Eigen::Dynamic>
std::unique_ptr<ITrajectoryInterpolator<DOF>> createRuckigInterpolator(const ControlParameters<DOF>& params);

template<int DOF = Eigen::Dynamic>
std::unique_ptr<ITrajectoryInterpolator<DOF>> createToppraInterpolator(const ControlParameters<DOF>& params, 
                                                                       const std::string& urdf_path);

} // namespace trajectory

#endif // INTERPOLATOR_INTERFACE_H
