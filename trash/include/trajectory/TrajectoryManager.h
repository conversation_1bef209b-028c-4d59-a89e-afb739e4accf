#ifndef TRAJECTORY_MANAGER_H
#define TRAJECTORY_MANAGER_H

#include "TrajectoryTypes.h"
#include "ToppraInterpolator.h"
#include "online_tracker.h"
#include <memory>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <queue>
#include <functional>

namespace trajectory {

// OnlineTracker已通过头文件包含

class TrajectoryManager {
public:
    using EventCallback = std::function<void(const ModeTransitionEvent&)>;

    // 构造函数现在接受控制参数
    explicit TrajectoryManager(const std::string& urdf_path, const ControlParameters& params);

private:
    // 核心组件 - 使用基类指针支持动态DOF
    std::unique_ptr<TrajInterpolator<6>> offline_interpolator_;  // 支持6DOF
    std::unique_ptr<OnlineTracker> online_tracker_;

    // 控制参数
    ControlParameters control_params_;

    // 状态管理
    std::atomic<TrajectoryMode> current_mode_;
    ThreadSafeTrajectoryState current_state_;

    // 双缓冲机制
    TrajectoryBuffer active_buffer_;
    TrajectoryBuffer back_buffer_;
    mutable std::mutex buffer_mutex_;

    // 时间管理
    std::atomic<double> current_time_;
    std::atomic<double> start_time_;

    // 模式切换
    std::queue<ModeTransitionEvent> transition_queue_;
    std::mutex transition_mutex_;
    std::condition_variable transition_cv_;
    std::thread transition_thread_;
    std::atomic<bool> transition_thread_running_;

    // 事件回调
    EventCallback event_callback_;

    // 配置参数
    std::string urdf_path_;
    Eigen::VectorXd max_velocity_;
    Eigen::VectorXd max_acceleration_;
    Eigen::VectorXd max_jerk_;

public:
    ~TrajectoryManager();

    // 初始化和清理
    bool initialize();
    void shutdown();

    // 轨迹设置接口
    bool setOfflineTrajectory(const std::vector<Eigen::VectorXd>& waypoints, bool loop = false);
    void setOnlineTarget(const Eigen::VectorXd& target_position,
                        const Eigen::VectorXd& target_velocity = Eigen::VectorXd());

    // 模式控制
    void switchToOfflineMode();
    void switchToOnlineMode();
    void switchToIdleMode();

    // 状态查询
    TrajectoryMode getCurrentMode() const { return current_mode_.load(); }
    TrajectoryState getCurrentState() const { return current_state_.get(); }
    double getCurrentTime() const { return current_time_.load(); }
    bool isTransitioning() const;

    // 主要接口：获取当前目标状态
    TrajectoryState getCurrentTarget();

    // 时间更新
    void updateTime(double dt);
    void resetTime();

    // 约束设置
    void setConstraints(const Eigen::VectorXd& max_vel,
                       const Eigen::VectorXd& max_acc,
                       const Eigen::VectorXd& max_jerk);

    // 事件回调设置
    void setEventCallback(const EventCallback& callback) { event_callback_ = callback; }

    // 状态设置（用于初始化或外部状态同步）
    void setCurrentState(const TrajectoryState& state);

private:
    // 内部方法
    void initializeInterpolators();
    void transitionThreadFunction();
    void processTransitionEvent(const ModeTransitionEvent& event);

    // 模式切换处理
    void handleTransitionToOnline(const ModeTransitionEvent& event);
    void handleTransitionToOffline(const ModeTransitionEvent& event);
    TrajectoryBuffer generateTransitionTrajectory(const TrajectoryState& start_state,
                                                 const TrajectoryState& end_state,
                                                 double duration);

    // 缓冲区管理
    void swapBuffers();
    TrajectoryState getTargetFromActiveBuffer();
    TrajectoryState getTargetFromOnlineTracker();

    // 轨迹生成辅助方法
    bool generateOfflineTrajectory(const std::vector<Eigen::VectorXd>& waypoints,
                                  TrajectoryBuffer& buffer, bool loop = false);

    // 状态验证
    bool isValidState(const TrajectoryState& state) const;
    TrajectoryState clampState(const TrajectoryState& state) const;

    // 线程安全的模式切换请求
    void requestModeTransition(TrajectoryMode target_mode);
};

} // namespace trajectory

#endif // TRAJECTORY_MANAGER_H
