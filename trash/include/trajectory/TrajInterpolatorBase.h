#ifndef TRAJ_INTERPOLATOR_BASE_H
#define TRAJ_INTERPOLATOR_BASE_H

#include "InterpolatorInterface.h"
#include "TrajectoryTypes.h"
#include <Eigen/Dense>
#include <vector>
#include <string>

namespace trajectory {

// 兼容性接口 - 保持向后兼容
template <int N>
class TrajInterpolator {
public:
    using VectorNd = Eigen::Matrix<double, N, 1>;
    using MatrixNd = Eigen::Matrix<double, N, Eigen::Dynamic>;

    TrajInterpolator(double control_period, const std::string& urdf_path)
        : m_dt(control_period), m_urdf_path(urdf_path) {}
    virtual ~TrajInterpolator() = default;

    virtual void setConstraint(const VectorNd& max_vel,
                               const VectorNd& max_acc,
                               const VectorNd& max_jerk) {
        this->m_max_vel = max_vel;
        this->m_max_acc = max_acc;
        this->m_max_jerk = max_jerk;
    }

    // 离线计算接口（仅对离线库如TOPP-RA有效，在线库空实现）
    virtual void computeOffline(const std::vector<VectorNd>& waypoints) = 0;

    // 单步在线更新接口（仅对在线库如Ruckig有效，离线库空实现）
    virtual bool computeOnline(const VectorNd& current_position,
                               const VectorNd& current_velocity,
                               const VectorNd& current_acceleration,
                               const VectorNd& target_position,
                               const VectorNd& target_velocity,
                               const VectorNd& target_acceleration,
                               double time_step) {
        return false;
    }

    // 采样轨迹：按时间间隔采样，返回时间戳、位置、速度、加速度
    virtual void sample(double dt,
                        std::vector<double>& ts,
                        MatrixNd& pos,
                        MatrixNd& vel,
                        MatrixNd& acc) const = 0;

protected:
    VectorNd m_max_vel = VectorNd::Constant(2.0);   // 最大速度
    VectorNd m_max_acc = VectorNd::Constant(10.0);  // 最大加速度
    VectorNd m_max_jerk = VectorNd::Constant(20.0); // 最大加加速度（jerk）
    double m_dt;                                    // 控制周期
    std::string m_urdf_path;                        // URDF路径
};

// 新的统一插补器适配器 - 将旧接口适配到新接口
template<int DOF>
class LegacyInterpolatorAdapter : public TrajectoryInterpolatorBase<LegacyInterpolatorAdapter<DOF>, DOF> {
public:
    using BaseType = TrajectoryInterpolatorBase<LegacyInterpolatorAdapter<DOF>, DOF>;
    using StateType = typename BaseType::StateType;
    using VectorType = typename BaseType::VectorType;
    using BufferType = typename BaseType::BufferType;
    using ParametersType = typename BaseType::ParametersType;
    using ResultType = typename BaseType::ResultType;

private:
    std::unique_ptr<TrajInterpolator<DOF>> legacy_interpolator_;

public:
    explicit LegacyInterpolatorAdapter(std::unique_ptr<TrajInterpolator<DOF>> interpolator)
        : legacy_interpolator_(std::move(interpolator)) {}

    // 实现纯虚函数
    InterpolatorType getType() const override {
        return InterpolatorType::OFFLINE; // 假设旧接口主要用于离线
    }

    std::string getName() const override {
        return "LegacyAdapter";
    }

    bool initialize(const ParametersType& params) override {
        this->parameters_ = params;
        if constexpr (DOF != Eigen::Dynamic) {
            legacy_interpolator_->setConstraint(params.max_velocity,
                                              params.max_acceleration,
                                              params.max_jerk);
        }
        this->setState(InterpolatorState::READY);
        return true;
    }

    void setConstraints(const VectorType& max_vel,
                       const VectorType& max_acc,
                       const VectorType& max_jerk) override {
        if constexpr (DOF != Eigen::Dynamic) {
            legacy_interpolator_->setConstraint(max_vel, max_acc, max_jerk);
        }
    }

    ResultType computeOnline(const StateType& current_state,
                           const StateType& target_state,
                           double time_step) override {
        // 旧接口不支持在线计算
        return ResultType();
    }

    bool computeOffline(const std::vector<VectorType>& waypoints,
                       BufferType& output_buffer) override {
        try {
            legacy_interpolator_->computeOffline(waypoints);

            // 采样轨迹到缓冲区
            std::vector<double> timestamps;
            Eigen::Matrix<double, DOF, Eigen::Dynamic> positions, velocities, accelerations;

            legacy_interpolator_->sample(this->parameters_.control_period,
                                       timestamps, positions, velocities, accelerations);

            output_buffer.clear();
            output_buffer.reserve(timestamps.size());

            for (size_t i = 0; i < timestamps.size(); ++i) {
                StateType state;
                state.position = positions.col(i);
                state.velocity = velocities.col(i);
                state.acceleration = accelerations.col(i);
                state.timestamp = timestamps[i];
                state.valid = true;

                output_buffer.addState(state, timestamps[i]);
            }

            return true;
        } catch (const std::exception& e) {
            this->setError(e.what());
            return false;
        }
    }

    std::future<bool> computeOfflineAsync(const std::vector<VectorType>& waypoints,
                                        BufferType& output_buffer) override {
        return std::async(std::launch::async, [this, waypoints, &output_buffer]() {
            return computeOffline(waypoints, output_buffer);
        });
    }

    void shutdown() override {
        legacy_interpolator_.reset();
        this->setState(InterpolatorState::IDLE);
    }
};

} // namespace trajectory

#endif // TRAJ_INTERPOLATOR_BASE_H