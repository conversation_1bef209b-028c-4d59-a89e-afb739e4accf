#ifndef UNIFIED_CONTROLLER_H
#define UNIFIED_CONTROLLER_H

#include "TrajectoryManager.h"
#include "TrajectoryTypes.h"
#include <chrono>
#include <thread>
#include <atomic>
#include <iostream>
#include <iomanip>
#include <functional>

namespace trajectory {

class UnifiedController {
public:
    using ServoCommandCallback = std::function<void(const TrajectoryState&)>;
    using EventCallback = std::function<void(const std::string&, const TrajectoryState&)>;

private:
    // 核心组件
    std::unique_ptr<TrajectoryManager> trajectory_manager_;
    
    // 控制循环管理
    std::atomic<bool> running_;
    std::thread control_thread_;
    
    // 时间管理
    std::chrono::steady_clock::time_point start_time_;
    std::chrono::steady_clock::time_point next_cycle_;
    
    // 统计信息
    std::atomic<size_t> cycle_count_;
    std::atomic<double> max_cycle_time_;
    std::atomic<double> avg_cycle_time_;
    std::atomic<double> total_cycle_time_;
    
    // 回调函数
    ServoCommandCallback servo_callback_;
    EventCallback event_callback_;
    
    // 配置参数
    std::string urdf_path_;
    ControlParameters control_params_;
    bool enable_statistics_;
    bool enable_real_time_output_;

public:
    // 构造函数 - 支持自定义参数
    explicit UnifiedController(const std::string& urdf_path,
                              const ControlParameters& params = ControlParameters());
    ~UnifiedController();

    // 初始化和控制
    bool initialize();
    void start();
    void stop();
    void shutdown();
    
    // 轨迹管理接口
    bool setOfflineTrajectory(const std::vector<Eigen::VectorXd>& waypoints, bool loop = false);
    void setOnlineTarget(const Eigen::VectorXd& target_position, 
                        const Eigen::VectorXd& target_velocity = Eigen::VectorXd::Zero(6));
    
    // 模式控制
    void switchToOfflineMode();
    void switchToOnlineMode();
    void switchToIdleMode();
    
    // 状态查询
    TrajectoryMode getCurrentMode() const;
    TrajectoryState getCurrentState() const;
    bool isRunning() const { return running_.load(); }
    bool isTransitioning() const;
    
    // 参数配置
    void setControlParameters(const ControlParameters& params);
    const ControlParameters& getControlParameters() const { return control_params_; }

    // 约束设置（便利方法）
    void setConstraints(const Eigen::VectorXd& max_vel,
                       const Eigen::VectorXd& max_acc,
                       const Eigen::VectorXd& max_jerk);
    
    // 回调设置
    void setServoCommandCallback(const ServoCommandCallback& callback) { servo_callback_ = callback; }
    void setEventCallback(const EventCallback& callback) { event_callback_ = callback; }
    
    // 配置选项
    void enableStatistics(bool enable) { enable_statistics_ = enable; }
    void enableRealTimeOutput(bool enable) { enable_real_time_output_ = enable; }
    
    // 统计信息
    struct Statistics {
        size_t total_cycles;
        double max_cycle_time_ms;
        double avg_cycle_time_ms;
        double control_frequency_hz;
        double uptime_seconds;
    };
    
    Statistics getStatistics() const;
    void resetStatistics();
    void printStatistics() const;
    
    // 状态设置（用于初始化）
    void setInitialState(const TrajectoryState& state);

private:
    // 主控制循环
    void controlLoop();
    
    // 内部方法
    void sendServoCommand(const TrajectoryState& target);
    double getCurrentTime() const;
    void updateStatistics(double cycle_time_ms);
    void handleTrajectoryManagerEvent(const ModeTransitionEvent& event);
    
    // 实时性能监控
    void checkRealTimePerformance(double cycle_time_ms);
    void printRealTimeStatus(const TrajectoryState& current_state, double cycle_time_ms);
};

} // namespace trajectory

#endif // UNIFIED_CONTROLLER_H
