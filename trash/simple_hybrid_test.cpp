#include "trajectory/HybridTrajectoryPlanner.hpp"
#include "trajectory/TrajectorySegmentManager.hpp"
#include "trajectory/TransitionPlanner.hpp"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <iomanip>

// 简单的混合轨迹测试
constexpr int DOF = 6;

void testHybridTrajectoryPlanner() {
    std::cout << "=== 测试混合轨迹规划器 ===" << std::endl;

    // 创建混合轨迹规划器
    HybridTrajectoryPlanner<DOF> planner(0.001);

    // 设置约束
    MotionConstraints<DOF> constraints;
    constraints.max_velocity.setConstant(1.0);
    constraints.max_acceleration.setConstant(2.0);
    constraints.max_jerk.setConstant(10.0);
    planner.setConstraints(constraints);

    // 启动规划器
    if (!planner.start()) {
        std::cerr << "启动规划器失败" << std::endl;
        return;
    }

    // 创建测试轨迹
    std::vector<MotionState<DOF>> waypoints;
    
    // 起点
    MotionState<DOF> start;
    start.position.setZero();
    start.velocity.setZero();
    start.acceleration.setZero();
    waypoints.push_back(start);

    // 终点
    MotionState<DOF> end;
    end.position.setConstant(0.5);
    end.velocity.setZero();
    end.acceleration.setZero();
    waypoints.push_back(end);

    // 设置初始状态
    planner.updateCurrentState(start);

    // 添加轨迹段
    if (planner.addTrajectorySegment(waypoints)) {
        std::cout << "轨迹段添加成功" << std::endl;

        // 模拟执行
        double time = 0.0;
        double dt = 0.01;  // 10ms
        
        for (int i = 0; i < 110; ++i) {  // 5秒
            auto target = planner.getCurrentTarget(time);
            
            if (target.valid) {
                if (i % 10 == 0) {  // 每秒打印一次
                    std::cout << "t=" << std::fixed << std::setprecision(2) << time 
                              << "s, pos=[" << std::setprecision(3) << target.position.transpose() 
                              << "]" << std::endl;
                }
            }
            
            time += dt;
            //std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    } else {
        std::cerr << "轨迹段添加失败" << std::endl;
    }

    planner.stop();
    std::cout << "混合轨迹规划器测试完成\n" << std::endl;
}

void testTrajectorySegmentManager() {
    std::cout << "=== 测试轨迹段管理器 ===" << std::endl;

    // 创建混合规划器
    auto hybrid_planner = std::make_shared<HybridTrajectoryPlanner<DOF>>(0.001);
    
    // 设置约束
    MotionConstraints<DOF> constraints;
    constraints.max_velocity.setConstant(1.0);
    constraints.max_acceleration.setConstant(2.0);
    constraints.max_jerk.setConstant(10.0);
    hybrid_planner->setConstraints(constraints);

    // 创建段管理器
    TrajectorySegmentManager<DOF> manager(hybrid_planner);

    // 设置回调
    manager.setSegmentCallback([](size_t segment_id, SegmentStatus status) {
        std::cout << "段 " << segment_id << " 状态: " << static_cast<int>(status) << std::endl;
    });

    std::cout << "0000000000" << std::endl;
    // 启动管理器
    if (!manager.start()) {
        std::cerr << "启动段管理器失败" << std::endl;
        return;
    }
    std::cout << "1111111111" << std::endl;

    // 创建轨迹段元数据
    SegmentMetadata<DOF> metadata1;
    metadata1.name = "Segment1";
    metadata1.description = "Move to position 0.5";
    
    // 第一段：原点到(0.5, 0.5, ...)
    std::vector<MotionState<DOF>> waypoints1;

    MotionState<DOF> start1;
    start1.position.setZero();
    start1.velocity.setZero();
    start1.acceleration.setZero();
    waypoints1.push_back(start1);

    MotionState<DOF> end1;
    end1.position.setConstant(0.5);
    end1.velocity.setZero();
    end1.acceleration.setZero();
    waypoints1.push_back(end1);

    size_t segment1 = manager.addSegment(waypoints1, metadata1);
    std::cout << "创建段1: " << segment1 << std::endl;

    // 第二段：从(0.5, 0.5, ...)到(1.0, 0.0, ...)
    SegmentMetadata<DOF> metadata2;
    metadata2.name = "Segment2";
    metadata2.description = "Move to final position";
    metadata2.dependencies.push_back(segment1);

    std::vector<MotionState<DOF>> waypoints2;
    waypoints2.push_back(end1);  // 起点是第一段的终点

    MotionState<DOF> end2;
    end2.position.setZero();
    end2.position[0] = 1.0;
    end2.velocity.setZero();
    end2.acceleration.setZero();
    waypoints2.push_back(end2);

    size_t segment2 = manager.addSegment(waypoints2, metadata2);
    std::cout << "创建段2: " << segment2 << std::endl;

    // 执行第一个段
    if (manager.executeSegment(segment1)) {
        std::cout << "开始执行段1" << std::endl;

        // 等待执行完成
        std::this_thread::sleep_for(std::chrono::seconds(3));

        std::cout << "段1执行完成，开始执行段2" << std::endl;

        // 执行第二个段
        if (manager.executeSegment(segment2)) {
            std::cout << "开始执行段2" << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(3));
        }
    }

    manager.stop();
    std::cout << "轨迹段管理器测试完成\n" << std::endl;
}

void testTransitionPlanner() {
    std::cout << "=== 测试过渡规划器 ===" << std::endl;

    TransitionPlanner<DOF> planner(0.001);

    // 设置约束
    TransitionConstraints<DOF> constraints;
    constraints.max_velocity.setConstant(1.0);
    constraints.max_acceleration.setConstant(2.0);
    constraints.max_jerk.setConstant(10.0);
    planner.setConstraints(constraints);

    // 设置配置
    TransitionConfig config;
    config.type = TransitionType::OPTIMAL_TIME;
    config.ensure_continuity = true;
    planner.setConfig(config);

    // 定义起始和目标状态
    MotionState<DOF> from_state;
    from_state.position.setZero();
    from_state.velocity.setZero();
    from_state.acceleration.setZero();

    MotionState<DOF> to_state;
    to_state.position.setConstant(0.8);
    to_state.velocity.setZero();
    to_state.acceleration.setZero();

    // 规划过渡轨迹
    auto result = planner.planTransition(from_state, to_state);

    if (result.success) {
        std::cout << "过渡规划成功:" << std::endl;
        std::cout << "  持续时间: " << result.duration << "s" << std::endl;
        std::cout << "  轨迹点数: " << result.trajectory_points.size() << std::endl;
        std::cout << "  平滑度分数: " << result.smoothness_score << std::endl;
        
        if (result.max_velocity_violation > 0) {
            std::cout << "  速度约束违反: " << result.max_velocity_violation << std::endl;
        }
        if (result.max_acceleration_violation > 0) {
            std::cout << "  加速度约束违反: " << result.max_acceleration_violation << std::endl;
        }
    } else {
        std::cout << "过渡规划失败: " << result.error_message << std::endl;
    }

    std::cout << "过渡规划器测试完成\n" << std::endl;
}

void testEmergencyTransition() {
    std::cout << "=== 测试紧急过渡 ===" << std::endl;

    auto emergency_planner = TransitionPlannerFactory<DOF>::createForEmergency(0.001);

    // 当前运动状态（高速运动）
    MotionState<DOF> current_state;
    current_state.position.setConstant(0.5);
    current_state.velocity.setConstant(0.8);  // 高速
    current_state.acceleration.setConstant(0.2);

    // 安全状态（停止）
    MotionState<DOF> safe_state;
    safe_state.position = current_state.position;
    safe_state.velocity.setZero();
    safe_state.acceleration.setZero();

    // 规划紧急过渡
    auto result = emergency_planner->planEmergencyTransition(current_state, safe_state, 1.0);

    if (result.success) {
        std::cout << "紧急过渡规划成功:" << std::endl;
        std::cout << "  停止时间: " << result.duration << "s" << std::endl;
        std::cout << "  轨迹点数: " << result.trajectory_points.size() << std::endl;
    } else {
        std::cout << "紧急过渡规划失败: " << result.error_message << std::endl;
    }

    std::cout << "紧急过渡测试完成\n" << std::endl;
}

int main() {
    std::cout << "开始混合轨迹控制组件测试...\n" << std::endl;

    try {
        testHybridTrajectoryPlanner();
        testTrajectorySegmentManager();
        testTransitionPlanner();
        testEmergencyTransition();

        std::cout << "所有测试完成!" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
