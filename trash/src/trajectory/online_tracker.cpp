#include "trajectory/online_tracker.h"
#include <iostream>
#include <algorithm>

namespace trajectory {

void OnlineTracker::setCurrentState(const TrajectoryState& state) {
    std::lock_guard<std::mutex> lock(state_mutex_);
    current_state_ = state;
    
    // 检查是否到达目标
    if (target_state_.valid && isNearTarget()) {
        target_reached_.store(true);
    }
}

void OnlineTracker::setTarget(const Eigen::VectorXd& target_position, 
                             const Eigen::VectorXd& target_velocity) {
    if (target_position.size() != control_params_.dof) {
        std::cerr << "Invalid target position size: " << target_position.size()
                  << ", expected: " << control_params_.dof << std::endl;
        return;
    }

    // 如果没有提供目标速度，使用零向量
    Eigen::VectorXd vel = target_velocity.size() == 0 ?
                         Eigen::VectorXd::Zero(control_params_.dof) : target_velocity;

    if (vel.size() != control_params_.dof) {
        std::cerr << "Invalid target velocity size: " << vel.size()
                  << ", expected: " << control_params_.dof << std::endl;
        return;
    }
    
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    // 设置新目标
    target_state_.position = target_position;
    target_state_.velocity = vel;
    target_state_.acceleration = Eigen::VectorXd::Zero(control_params_.dof);
    target_state_.valid = true;
    
    // 标记有新目标
    has_new_target_.store(true);
    target_reached_.store(false);
    
    // 清空轨迹缓存，强制重新规划
    trajectory_cache_.clear();
    cache_index_ = 0;
    cache_duration_ = 0.0;
}

TrajectoryState OnlineTracker::getNextState(double dt) {
    // 如果有新目标，重新规划轨迹
    if (has_new_target_.load()) {
        if (planTrajectory()) {
            has_new_target_.store(false);
            updateTrajectoryCache();
        }
    }
    
    // 从缓存中获取下一个状态
    if (!trajectory_cache_.empty() && cache_index_ < trajectory_cache_.size()) {
        TrajectoryState next_state = trajectory_cache_[cache_index_];
        
        // 更新当前状态
        {
            std::lock_guard<std::mutex> lock(state_mutex_);
            current_state_ = next_state;
        }
        
        // 移动到下一个缓存点
        cache_index_++;
        
        // 检查是否到达目标
        if (cache_index_ >= trajectory_cache_.size() && target_state_.valid) {
            if (isNearTarget()) {
                target_reached_.store(true);
            }
        }
        
        return next_state;
    }
    
    // 如果没有缓存或已到达末尾，返回当前状态
    std::lock_guard<std::mutex> lock(state_mutex_);
    return current_state_;
}

TrajectoryState OnlineTracker::getCurrentState() const {
    std::lock_guard<std::mutex> lock(state_mutex_);
    return current_state_;
}

TrajectoryState OnlineTracker::getTargetState() const {
    std::lock_guard<std::mutex> lock(state_mutex_);
    return target_state_;
}

void OnlineTracker::setConstraints(const Eigen::VectorXd& max_vel, 
                                  const Eigen::VectorXd& max_acc, 
                                  const Eigen::VectorXd& max_jerk) {
    if (max_vel.size() != DOF || max_acc.size() != DOF || max_jerk.size() != DOF) {
        std::cerr << "Invalid constraint vector sizes" << std::endl;
        return;
    }
    
    max_velocity_ = max_vel;
    max_acceleration_ = max_acc;
    max_jerk_ = max_jerk;
    
    // 更新插值器约束
    interpolator_.setConstraint(max_velocity_, max_acceleration_, max_jerk_);
}

void OnlineTracker::reset() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    current_state_ = TrajectoryState(DOF);
    target_state_ = TrajectoryState(DOF);
    
    has_new_target_.store(false);
    target_reached_.store(true);
    
    trajectory_cache_.clear();
    cache_index_ = 0;
    cache_duration_ = 0.0;
}

bool OnlineTracker::planTrajectory() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    if (!current_state_.valid || !target_state_.valid) {
        return false;
    }
    
    try {
        // 使用Ruckig进行在线轨迹规划
        bool success = interpolator_.computeOnline(
            current_state_.position,
            current_state_.velocity,
            current_state_.acceleration,
            target_state_.position,
            target_state_.velocity,
            target_state_.acceleration,
            control_params_.control_period
        );
        
        if (!success) {
            std::cerr << "Ruckig trajectory planning failed" << std::endl;
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error in trajectory planning: " << e.what() << std::endl;
        return false;
    }
}

void OnlineTracker::updateTrajectoryCache() {
    try {
        // 采样轨迹
        std::vector<double> timestamps;
        Eigen::Matrix<double, DOF, Eigen::Dynamic> positions, velocities, accelerations;
        
        interpolator_.sample(control_params_.control_period, timestamps, positions, velocities, accelerations);
        
        // 清空并重新填充缓存
        trajectory_cache_.clear();
        trajectory_cache_.reserve(timestamps.size());
        
        for (size_t i = 0; i < timestamps.size(); ++i) {
            TrajectoryState state;
            // state.position = positions.col(i);
            // state.velocity = velocities.col(i);
            // state.acceleration = accelerations.col(i);
            state.timestamp = timestamps[i];
            state.valid = true;
            
            trajectory_cache_.push_back(state);
        }
        
        cache_index_ = 0;
        cache_duration_ = timestamps.empty() ? 0.0 : timestamps.back();
        
        std::cout << "Trajectory cache updated with " << trajectory_cache_.size() 
                  << " points, duration: " << cache_duration_ << "s" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error updating trajectory cache: " << e.what() << std::endl;
        trajectory_cache_.clear();
        cache_index_ = 0;
        cache_duration_ = 0.0;
    }
}

bool OnlineTracker::isNearTarget(double position_tolerance, double velocity_tolerance) const {
    if (!current_state_.valid || !target_state_.valid) {
        return false;
    }
    
    double pos_error = (current_state_.position - target_state_.position).norm();
    double vel_error = (current_state_.velocity - target_state_.velocity).norm();
    
    return pos_error < position_tolerance && vel_error < velocity_tolerance;
}

} // namespace trajectory
