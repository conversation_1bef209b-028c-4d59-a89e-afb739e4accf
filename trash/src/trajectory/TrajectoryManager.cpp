#include "trajectory/TrajectoryManager.h"
#include "trajectory/online_tracker.h"
#include <iostream>
#include <algorithm>

namespace trajectory {

TrajectoryManager::TrajectoryManager(const std::string& urdf_path, const ControlParameters& params)
    : urdf_path_(urdf_path)
    , control_params_(params)
    , current_mode_(TrajectoryMode::IDLE)
    , current_state_(params.dof)
    , current_time_(0.0)
    , start_time_(0.0)
    , transition_thread_running_(false)
    , max_velocity_(params.max_velocity)
    , max_acceleration_(params.max_acceleration)
    , max_jerk_(params.max_jerk) {
}

TrajectoryManager::~TrajectoryManager() {
    shutdown();
}

bool TrajectoryManager::initialize() {
    try {
        // 初始化插值器
        initializeInterpolators();
        
        // 启动过渡处理线程
        transition_thread_running_ = true;
        transition_thread_ = std::thread(&TrajectoryManager::transitionThreadFunction, this);
        
        std::cout << "TrajectoryManager initialized successfully" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize TrajectoryManager: " << e.what() << std::endl;
        return false;
    }
}

void TrajectoryManager::shutdown() {
    // 停止过渡线程
    transition_thread_running_ = false;
    transition_cv_.notify_all();
    
    if (transition_thread_.joinable()) {
        transition_thread_.join();
    }
    
    // 清理资源
    offline_interpolator_.reset();
    online_tracker_.reset();
    
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    active_buffer_.clear();
    back_buffer_.clear();
}

void TrajectoryManager::initializeInterpolators() {
    // 初始化离线插值器 - 根据DOF创建合适的插值器
    if (control_params_.dof == 6) {
        offline_interpolator_ = std::make_unique<ToppraInterpolator<6>>(
            control_params_.control_period, urdf_path_);
    } else {
        std::cerr << "Warning: Only 6DOF is currently supported, using 6DOF interpolator" << std::endl;
        offline_interpolator_ = std::make_unique<ToppraInterpolator<6>>(
            control_params_.control_period, urdf_path_);
    }
    offline_interpolator_->setConstraint(max_velocity_, max_acceleration_, max_jerk_);

    // 初始化在线跟踪器，传递控制参数
    online_tracker_ = std::make_unique<OnlineTracker>(urdf_path_, control_params_);
    online_tracker_->setConstraints(max_velocity_, max_acceleration_, max_jerk_);

    std::cout << "TrajectoryManager initialized successfully" << std::endl;
}

bool TrajectoryManager::setOfflineTrajectory(const std::vector<Eigen::VectorXd>& waypoints, bool loop) {
    if (waypoints.empty()) {
        std::cerr << "Empty waypoints provided" << std::endl;
        return false;
    }
    
    try {
        // 在后台缓冲区生成轨迹
        std::lock_guard<std::mutex> lock(buffer_mutex_);
        if (!generateOfflineTrajectory(waypoints, back_buffer_, loop)) {
            std::cerr << "Failed to generate offline trajectory" << std::endl;
            return false;
        }
        
        std::cout << "Offline trajectory generated with " << back_buffer_.states.size() 
                  << " points, duration: " << back_buffer_.duration << "s" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error setting offline trajectory: " << e.what() << std::endl;
        return false;
    }
}

void TrajectoryManager::setOnlineTarget(const Eigen::VectorXd& target_position, 
                                       const Eigen::VectorXd& target_velocity) {
    if (online_tracker_) {
        online_tracker_->setTarget(target_position, target_velocity);
    }
}

void TrajectoryManager::switchToOfflineMode() {
    requestModeTransition(TrajectoryMode::OFFLINE);
}

void TrajectoryManager::switchToOnlineMode() {
    requestModeTransition(TrajectoryMode::ONLINE);
}

void TrajectoryManager::switchToIdleMode() {
    requestModeTransition(TrajectoryMode::IDLE);
}

bool TrajectoryManager::isTransitioning() const {
    TrajectoryMode mode = current_mode_.load();
    return mode == TrajectoryMode::TRANSITION_TO_ONLINE || 
           mode == TrajectoryMode::TRANSITION_TO_OFFLINE;
}

TrajectoryState TrajectoryManager::getCurrentTarget() {
    TrajectoryMode mode = current_mode_.load();
    
    switch (mode) {
        case TrajectoryMode::OFFLINE:
        case TrajectoryMode::TRANSITION_TO_OFFLINE:
            return getTargetFromActiveBuffer();
            
        case TrajectoryMode::ONLINE:
        case TrajectoryMode::TRANSITION_TO_ONLINE:
            return getTargetFromOnlineTracker();
            
        case TrajectoryMode::IDLE:
        default:
            return current_state_.get(); // 保持当前状态
    }
}

void TrajectoryManager::updateTime(double dt) {
    double new_time = current_time_.load() + dt;
    current_time_.store(new_time);
    
    // 更新活动缓冲区的索引
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    if (!active_buffer_.isEmpty()) {
        active_buffer_.updateIndex(new_time - start_time_.load());
    }
}

void TrajectoryManager::resetTime() {
    current_time_.store(0.0);
    start_time_.store(0.0);
    
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    active_buffer_.current_index = 0;
}

void TrajectoryManager::setConstraints(const Eigen::VectorXd& max_vel, 
                                      const Eigen::VectorXd& max_acc, 
                                      const Eigen::VectorXd& max_jerk) {
    max_velocity_ = max_vel;
    max_acceleration_ = max_acc;
    max_jerk_ = max_jerk;
    
    if (offline_interpolator_) {
        offline_interpolator_->setConstraint(max_vel, max_acc, max_jerk);
    }
    
    if (online_tracker_) {
        online_tracker_->setConstraints(max_vel, max_acc, max_jerk);
    }
}

void TrajectoryManager::setCurrentState(const TrajectoryState& state) {
    current_state_.set(state);
    
    // 同步到在线跟踪器
    if (online_tracker_) {
        online_tracker_->setCurrentState(state);
    }
}

void TrajectoryManager::requestModeTransition(TrajectoryMode target_mode) {
    TrajectoryMode current = current_mode_.load();
    
    if (current == target_mode) {
        return; // 已经是目标模式
    }
    
    // 创建过渡事件
    TrajectoryState current_state = current_state_.get();
    ModeTransitionEvent event(current, target_mode, current_state);
    
    // 添加到过渡队列
    {
        std::lock_guard<std::mutex> lock(transition_mutex_);
        transition_queue_.push(event);
    }
    transition_cv_.notify_one();
}

TrajectoryState TrajectoryManager::getTargetFromActiveBuffer() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    if (active_buffer_.isEmpty()) {
        return current_state_.get();
    }
    
    double relative_time = current_time_.load() - start_time_.load();
    TrajectoryState target = active_buffer_.getStateAtTime(relative_time);
    
    if (target.valid) {
        current_state_.set(target);
    }
    
    return target;
}

TrajectoryState TrajectoryManager::getTargetFromOnlineTracker() {
    if (!online_tracker_) {
        return current_state_.get();
    }
    
    TrajectoryState target = online_tracker_->getNextState(control_params_.control_period);
    
    if (target.valid) {
        current_state_.set(target);
    }
    
    return target;
}

bool TrajectoryManager::generateOfflineTrajectory(const std::vector<Eigen::VectorXd>& waypoints,
                                                  TrajectoryBuffer& buffer, bool loop) {
    if (!offline_interpolator_) {
        return false;
    }

    try {
        // 转换为固定大小的向量类型（6DOF）
        std::vector<Eigen::Matrix<double, 6, 1>> fixed_waypoints;
        fixed_waypoints.reserve(waypoints.size());

        for (const auto& wp : waypoints) {
            if (wp.size() != control_params_.dof) {
                std::cerr << "Invalid waypoint size: " << wp.size()
                          << ", expected: " << control_params_.dof << std::endl;
                return false;
            }

            if (wp.size() != 6) {
                std::cerr << "Current implementation only supports 6DOF, got: " << wp.size() << std::endl;
                return false;
            }

            Eigen::Matrix<double, 6, 1> fixed_wp;
            for (int i = 0; i < 6; ++i) {
                fixed_wp[i] = wp[i];
            }
            fixed_waypoints.push_back(fixed_wp);
        }

        // 使用TOPPRA生成轨迹
        offline_interpolator_->computeOffline(fixed_waypoints);
        
        // 采样轨迹
        std::vector<double> timestamps;
        Eigen::Matrix<double, 6, Eigen::Dynamic> positions, velocities, accelerations;
        offline_interpolator_->sample(control_params_.control_period, timestamps, positions, velocities, accelerations);
        
        // 填充缓冲区
        buffer.clear();
        buffer.states.reserve(timestamps.size());
        buffer.timestamps = timestamps;
        buffer.is_looping = loop;
        
        for (size_t i = 0; i < timestamps.size(); ++i) {
            TrajectoryState state;
            state.position = positions.col(i);
            state.velocity = velocities.col(i);
            state.acceleration = accelerations.col(i);
            state.timestamp = timestamps[i];
            state.valid = true;
            buffer.states.push_back(state);
        }
        
        buffer.duration = timestamps.empty() ? 0.0 : timestamps.back();
        buffer.is_valid = true;
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error generating offline trajectory: " << e.what() << std::endl;
        return false;
    }
}

void TrajectoryManager::swapBuffers() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    std::swap(active_buffer_, back_buffer_);
    start_time_.store(current_time_.load());
}

bool TrajectoryManager::isValidState(const TrajectoryState& state) const {
    return state.valid &&
           state.position.size() == control_params_.dof &&
           state.velocity.size() == control_params_.dof &&
           state.acceleration.size() == control_params_.dof;
}

TrajectoryState TrajectoryManager::clampState(const TrajectoryState& state) const {
    TrajectoryState clamped = state;
    
    // 限制速度
    for (int i = 0; i < clamped.velocity.size(); ++i) {
        clamped.velocity[i] = std::clamp(clamped.velocity[i], 
                                        -max_velocity_[i], max_velocity_[i]);
    }
    
    // 限制加速度
    for (int i = 0; i < clamped.acceleration.size(); ++i) {
        clamped.acceleration[i] = std::clamp(clamped.acceleration[i], 
                                           -max_acceleration_[i], max_acceleration_[i]);
    }
    
    return clamped;
}

void TrajectoryManager::transitionThreadFunction() {
    while (transition_thread_running_) {
        std::unique_lock<std::mutex> lock(transition_mutex_);

        // 等待过渡事件
        transition_cv_.wait(lock, [this] {
            return !transition_queue_.empty() || !transition_thread_running_;
        });

        if (!transition_thread_running_) break;

        // 处理所有待处理的过渡事件
        while (!transition_queue_.empty()) {
            ModeTransitionEvent event = transition_queue_.front();
            transition_queue_.pop();
            lock.unlock();

            processTransitionEvent(event);

            lock.lock();
        }
    }
}

void TrajectoryManager::processTransitionEvent(const ModeTransitionEvent& event) {
    std::cout << "Processing mode transition from " << static_cast<int>(event.from_mode)
              << " to " << static_cast<int>(event.to_mode) << std::endl;

    switch (event.to_mode) {
        case TrajectoryMode::ONLINE:
            handleTransitionToOnline(event);
            break;

        case TrajectoryMode::OFFLINE:
            handleTransitionToOffline(event);
            break;

        case TrajectoryMode::IDLE:
            current_mode_.store(TrajectoryMode::IDLE);
            break;

        default:
            std::cerr << "Invalid transition target mode" << std::endl;
            return;
    }

    // 通知事件回调
    if (event_callback_) {
        event_callback_(event);
    }
}

void TrajectoryManager::handleTransitionToOnline(const ModeTransitionEvent& event) {
    // 设置过渡状态
    current_mode_.store(TrajectoryMode::TRANSITION_TO_ONLINE);

    // 初始化在线跟踪器的当前状态
    if (online_tracker_) {
        online_tracker_->setCurrentState(event.transition_start_state);
    }

    // 如果需要平滑过渡，生成过渡轨迹
    if (event.requires_smooth_transition && event.transition_duration > 0) {
        // 这里可以生成一个短暂的过渡轨迹
        // 暂时直接切换到在线模式
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }

    // 切换到在线模式
    current_mode_.store(TrajectoryMode::ONLINE);
    std::cout << "Switched to ONLINE mode" << std::endl;
}

void TrajectoryManager::handleTransitionToOffline(const ModeTransitionEvent& event) {
    // 设置过渡状态
    current_mode_.store(TrajectoryMode::TRANSITION_TO_OFFLINE);

    // 检查是否有有效的离线轨迹
    {
        std::lock_guard<std::mutex> lock(buffer_mutex_);
        if (back_buffer_.isEmpty()) {
            std::cerr << "No offline trajectory available, staying in current mode" << std::endl;
            current_mode_.store(event.from_mode);
            return;
        }

        // 交换缓冲区，激活离线轨迹
        swapBuffers();
    }

    // 如果需要平滑过渡
    if (event.requires_smooth_transition && event.transition_duration > 0) {
        // 生成从当前状态到离线轨迹起点的过渡轨迹
        TrajectoryState offline_start;
        {
            std::lock_guard<std::mutex> lock(buffer_mutex_);
            if (!active_buffer_.states.empty()) {
                offline_start = active_buffer_.states[0];
            }
        }

        if (offline_start.valid) {
            TrajectoryBuffer transition_buffer = generateTransitionTrajectory(
                event.transition_start_state, offline_start, event.transition_duration);

            if (!transition_buffer.isEmpty()) {
                // 暂时使用过渡轨迹
                std::lock_guard<std::mutex> lock(buffer_mutex_);
                back_buffer_ = std::move(transition_buffer);
                swapBuffers();

                // 等待过渡完成
                std::this_thread::sleep_for(
                    std::chrono::milliseconds(static_cast<int>(event.transition_duration * 1000)));

                // 切换回原始离线轨迹
                swapBuffers();
            }
        }
    }

    // 切换到离线模式
    current_mode_.store(TrajectoryMode::OFFLINE);
    std::cout << "Switched to OFFLINE mode" << std::endl;
}

TrajectoryBuffer TrajectoryManager::generateTransitionTrajectory(const TrajectoryState& start_state,
                                                                const TrajectoryState& end_state,
                                                                double duration) {
    TrajectoryBuffer buffer;

    if (!isValidState(start_state) || !isValidState(end_state) || duration <= 0) {
        return buffer;
    }

    try {
        // 使用简单的多项式插值生成过渡轨迹
        int num_points = static_cast<int>(duration / control_params_.control_period) + 1;
        buffer.states.reserve(num_points);
        buffer.timestamps.reserve(num_points);

        for (int i = 0; i < num_points; ++i) {
            double t = i * control_params_.control_period;
            double alpha = t / duration;

            // 使用三次多项式插值确保速度连续性
            double alpha2 = alpha * alpha;
            double alpha3 = alpha2 * alpha;
            double h1 = 2 * alpha3 - 3 * alpha2 + 1;
            double h2 = -2 * alpha3 + 3 * alpha2;
            double h3 = alpha3 - 2 * alpha2 + alpha;
            double h4 = alpha3 - alpha2;

            TrajectoryState state;
            state.position = h1 * start_state.position + h2 * end_state.position +
                           h3 * duration * start_state.velocity + h4 * duration * end_state.velocity;

            // 计算速度（对位置求导）
            double dh1 = 6 * alpha2 - 6 * alpha;
            double dh2 = -6 * alpha2 + 6 * alpha;
            double dh3 = 3 * alpha2 - 4 * alpha + 1;
            double dh4 = 3 * alpha2 - 2 * alpha;

            state.velocity = (dh1 * start_state.position + dh2 * end_state.position +
                            dh3 * duration * start_state.velocity + dh4 * duration * end_state.velocity) / duration;

            // 简化加速度计算
            state.acceleration = Eigen::VectorXd::Zero(control_params_.dof);

            state.timestamp = t;
            state.valid = true;

            buffer.states.push_back(clampState(state));
            buffer.timestamps.push_back(t);
        }

        buffer.duration = duration;
        buffer.is_valid = true;
        buffer.is_looping = false;

    } catch (const std::exception& e) {
        std::cerr << "Error generating transition trajectory: " << e.what() << std::endl;
        buffer.clear();
    }

    return buffer;
}

} // namespace trajectory
