#include "trajectory/UnifiedController.h"
#include <iostream>
#include <iomanip>

namespace trajectory {

UnifiedController::UnifiedController(const std::string& urdf_path,
                                     const ControlParameters& params)
    : urdf_path_(urdf_path)
    , control_params_(params)
    , running_(false)
    , cycle_count_(0)
    , max_cycle_time_(0.0)
    , avg_cycle_time_(0.0)
    , total_cycle_time_(0.0)
    , enable_statistics_(true)
    , enable_real_time_output_(false) {

    // 验证参数
    if (!control_params_.validate()) {
        throw std::invalid_argument("Invalid control parameters provided");
    }
}

UnifiedController::~UnifiedController() {
    shutdown();
}

bool UnifiedController::initialize() {
    try {
        // 创建轨迹管理器，传递控制参数
        trajectory_manager_ = std::make_unique<TrajectoryManager>(urdf_path_, control_params_);
        
        // 设置事件回调
        trajectory_manager_->setEventCallback(
            [this](const ModeTransitionEvent& event) {
                handleTrajectoryManagerEvent(event);
            });
        
        // 初始化轨迹管理器
        if (!trajectory_manager_->initialize()) {
            std::cerr << "Failed to initialize TrajectoryManager" << std::endl;
            return false;
        }
        
        std::cout << "UnifiedController initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize UnifiedController: " << e.what() << std::endl;
        return false;
    }
}

void UnifiedController::start() {
    if (running_.load()) {
        std::cout << "Controller is already running" << std::endl;
        return;
    }
    
    if (!trajectory_manager_) {
        std::cerr << "Controller not initialized. Call initialize() first." << std::endl;
        return;
    }
    
    // 重置统计信息
    resetStatistics();
    
    // 启动控制循环
    running_.store(true);
    control_thread_ = std::thread(&UnifiedController::controlLoop, this);
    
    std::cout << "UnifiedController started at " << control_params_.control_frequency
              << " Hz (" << control_params_.control_period * 1000 << " ms period)" << std::endl;
}

void UnifiedController::stop() {
    if (!running_.load()) {
        return;
    }
    
    std::cout << "Stopping UnifiedController..." << std::endl;
    running_.store(false);
    
    if (control_thread_.joinable()) {
        control_thread_.join();
    }
    
    if (enable_statistics_) {
        printStatistics();
    }
    
    std::cout << "UnifiedController stopped" << std::endl;
}

void UnifiedController::shutdown() {
    stop();
    
    if (trajectory_manager_) {
        trajectory_manager_->shutdown();
        trajectory_manager_.reset();
    }
}

bool UnifiedController::setOfflineTrajectory(const std::vector<Eigen::VectorXd>& waypoints, bool loop) {
    if (!trajectory_manager_) {
        std::cerr << "Controller not initialized" << std::endl;
        return false;
    }
    
    return trajectory_manager_->setOfflineTrajectory(waypoints, loop);
}

void UnifiedController::setOnlineTarget(const Eigen::VectorXd& target_position, 
                                       const Eigen::VectorXd& target_velocity) {
    if (!trajectory_manager_) {
        std::cerr << "Controller not initialized" << std::endl;
        return;
    }
    
    trajectory_manager_->setOnlineTarget(target_position, target_velocity);
}

void UnifiedController::switchToOfflineMode() {
    if (trajectory_manager_) {
        trajectory_manager_->switchToOfflineMode();
    }
}

void UnifiedController::switchToOnlineMode() {
    if (trajectory_manager_) {
        trajectory_manager_->switchToOnlineMode();
    }
}

void UnifiedController::switchToIdleMode() {
    if (trajectory_manager_) {
        trajectory_manager_->switchToIdleMode();
    }
}

TrajectoryMode UnifiedController::getCurrentMode() const {
    return trajectory_manager_ ? trajectory_manager_->getCurrentMode() : TrajectoryMode::IDLE;
}

TrajectoryState UnifiedController::getCurrentState() const {
    return trajectory_manager_ ? trajectory_manager_->getCurrentState() : TrajectoryState();
}

bool UnifiedController::isTransitioning() const {
    return trajectory_manager_ ? trajectory_manager_->isTransitioning() : false;
}

void UnifiedController::setControlParameters(const ControlParameters& params) {
    if (!params.validate()) {
        throw std::invalid_argument("Invalid control parameters");
    }

    control_params_ = params;

    // 更新轨迹管理器的约束
    if (trajectory_manager_) {
        trajectory_manager_->setConstraints(
            control_params_.max_velocity,
            control_params_.max_acceleration,
            control_params_.max_jerk
        );
    }
}

void UnifiedController::setConstraints(const Eigen::VectorXd& max_vel,
                                      const Eigen::VectorXd& max_acc,
                                      const Eigen::VectorXd& max_jerk) {
    control_params_.setConstraints(max_vel, max_acc, max_jerk);

    if (trajectory_manager_) {
        trajectory_manager_->setConstraints(max_vel, max_acc, max_jerk);
    }
}

void UnifiedController::setInitialState(const TrajectoryState& state) {
    if (trajectory_manager_) {
        trajectory_manager_->setCurrentState(state);
    }
}

UnifiedController::Statistics UnifiedController::getStatistics() const {
    Statistics stats;
    stats.total_cycles = cycle_count_.load();
    stats.max_cycle_time_ms = max_cycle_time_.load();
    stats.avg_cycle_time_ms = avg_cycle_time_.load();
    stats.control_frequency_hz = control_params_.control_frequency;
    
    if (running_.load()) {
        auto current_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            current_time - start_time_).count();
        stats.uptime_seconds = duration / 1000.0;
    } else {
        stats.uptime_seconds = 0.0;
    }
    
    return stats;
}

void UnifiedController::resetStatistics() {
    cycle_count_.store(0);
    max_cycle_time_.store(0.0);
    avg_cycle_time_.store(0.0);
    total_cycle_time_.store(0.0);
    start_time_ = std::chrono::steady_clock::now();
}

void UnifiedController::printStatistics() const {
    Statistics stats = getStatistics();
    
    std::cout << "\n=== UnifiedController Statistics ===" << std::endl;
    std::cout << "Total cycles: " << stats.total_cycles << std::endl;
    std::cout << "Uptime: " << std::fixed << std::setprecision(2) << stats.uptime_seconds << " s" << std::endl;
    std::cout << "Target frequency: " << stats.control_frequency_hz << " Hz" << std::endl;
    std::cout << "Average cycle time: " << std::fixed << std::setprecision(3) 
              << stats.avg_cycle_time_ms << " ms" << std::endl;
    std::cout << "Max cycle time: " << std::fixed << std::setprecision(3) 
              << stats.max_cycle_time_ms << " ms" << std::endl;
    
    if (stats.avg_cycle_time_ms > 0) {
        double actual_freq = 1000.0 / stats.avg_cycle_time_ms;
        std::cout << "Actual frequency: " << std::fixed << std::setprecision(1) 
                  << actual_freq << " Hz" << std::endl;
    }
    std::cout << "===================================\n" << std::endl;
}

void UnifiedController::controlLoop() {
    const auto cycle_duration = std::chrono::microseconds(
        static_cast<int>(control_params_.control_period * 1000000));
    
    next_cycle_ = std::chrono::steady_clock::now();
    start_time_ = next_cycle_;
    
    std::cout << "Control loop started" << std::endl;
    
    while (running_.load()) {
        auto cycle_start = std::chrono::steady_clock::now();
        
        try {
            // 1. 获取当前目标状态
            TrajectoryState target = trajectory_manager_->getCurrentTarget();
            
            // 2. 发送伺服命令
            sendServoCommand(target);
            
            // 3. 更新轨迹时间
            trajectory_manager_->updateTime(control_params_.control_period);
            
            // 4. 计算循环时间并更新统计
            auto cycle_end = std::chrono::steady_clock::now();
            auto cycle_time = std::chrono::duration_cast<std::chrono::microseconds>(
                cycle_end - cycle_start).count() / 1000.0; // 转换为毫秒
            
            if (enable_statistics_) {
                updateStatistics(cycle_time);
            }
            
            // 5. 实时性能监控
            checkRealTimePerformance(cycle_time);
            
            // 6. 实时输出（如果启用）
            if (enable_real_time_output_) {
                printRealTimeStatus(target, cycle_time);
            }
            
        } catch (const std::exception& e) {
            std::cerr << "Error in control loop: " << e.what() << std::endl;
        }
        
        // 7. 等待下一个控制周期
        next_cycle_ += cycle_duration;
        std::this_thread::sleep_until(next_cycle_);
    }
    
    std::cout << "Control loop stopped" << std::endl;
}

void UnifiedController::sendServoCommand(const TrajectoryState& target) {
    if (servo_callback_) {
        servo_callback_(target);
    }
    // 如果没有设置回调，这里可以添加默认的伺服命令发送逻辑
}

double UnifiedController::getCurrentTime() const {
    if (!running_.load()) return 0.0;
    
    auto current = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
        current - start_time_).count();
    return duration / 1000000.0; // 转换为秒
}

void UnifiedController::updateStatistics(double cycle_time_ms) {
    // 简化的统计更新，避免原子操作兼容性问题
    size_t count = cycle_count_.load();
    cycle_count_.store(count + 1);

    // 更新最大循环时间
    double current_max = max_cycle_time_.load();
    if (cycle_time_ms > current_max) {
        max_cycle_time_.store(cycle_time_ms);
    }

    // 更新平均循环时间
    double current_total = total_cycle_time_.load();
    total_cycle_time_.store(current_total + cycle_time_ms);
    avg_cycle_time_.store((current_total + cycle_time_ms) / (count + 1));
}

void UnifiedController::handleTrajectoryManagerEvent(const ModeTransitionEvent& event) {
    std::string event_msg = "Mode transition: " + 
                           std::to_string(static_cast<int>(event.from_mode)) + 
                           " -> " + std::to_string(static_cast<int>(event.to_mode));
    
    if (event_callback_) {
        event_callback_(event_msg, event.transition_start_state);
    }
    
    std::cout << event_msg << std::endl;
}

void UnifiedController::checkRealTimePerformance(double cycle_time_ms) {
    const double target_cycle_time = 0.016 * 1000; // ms
    const double warning_threshold = target_cycle_time * 1.1; // 10% 超时警告
    const double error_threshold = target_cycle_time * 1.5;   // 50% 超时错误
    
    if (cycle_time_ms > error_threshold) {
        std::cerr << "ERROR: Severe cycle time violation! " 
                  << std::fixed << std::setprecision(3) << cycle_time_ms 
                  << " ms (target: " << target_cycle_time << " ms)" << std::endl;
    } else if (cycle_time_ms > warning_threshold) {
        std::cerr << "WARNING: Cycle time exceeded! " 
                  << std::fixed << std::setprecision(3) << cycle_time_ms 
                  << " ms (target: " << target_cycle_time << " ms)" << std::endl;
    }
}

void UnifiedController::printRealTimeStatus(const TrajectoryState& current_state, double cycle_time_ms) {
    static size_t print_counter = 0;
    const size_t print_interval = 100; // 每100个周期打印一次
    
    if (++print_counter >= print_interval) {
        print_counter = 0;
        
        std::cout << "Mode: " << static_cast<int>(getCurrentMode()) 
                  << " | Cycle: " << std::fixed << std::setprecision(2) << cycle_time_ms << " ms"
                  << " | Pos: [" << current_state.position.head(3).transpose() << "]" << std::endl;
    }
}

} // namespace trajectory
