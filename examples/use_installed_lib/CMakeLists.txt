cmake_minimum_required(VERSION 3.16)
project(robot_infra_example)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找已安装的robot_infra库
find_package(robot_infra REQUIRED)

# 创建示例程序
add_executable(simple_example simple_example.cpp)

# 链接库
target_link_libraries(simple_example robot_infra::robot_infra)

# 打印信息
message(STATUS "Found robot_infra version: ${robot_infra_VERSION}")
message(STATUS "robot_infra libraries: ${robot_infra_LIBRARIES}")
message(STATUS "robot_infra include dirs: ${robot_infra_INCLUDE_DIRS}")
