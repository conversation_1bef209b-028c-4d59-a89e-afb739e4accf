#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip> // For std::setprecision
#include <Eigen/Dense>

#include <toppra/algorithm/toppra.hpp>
#include <toppra/constraint/linear_joint_acceleration.hpp>
#include <toppra/constraint/linear_joint_velocity.hpp>
#include <toppra/parametrizer/spline.hpp>
#include <toppra/geometric_path/piecewise_poly_path.hpp>
#include <toppra/parametrizer/const_accel.hpp>
// #include <toppra/constraint/joint_torque/pinocchio.hpp>
// #include <toppra/solver/qpOASES-wrapper.hpp>
// #include <toppra/solver/glpk-wrapper.hpp>
#include <toppra/solver/seidel.hpp>
#include <toppra/toppra.hpp>


// 使用 Eigen 命名空间以简化代码
using namespace Eigen;
using namespace toppra;
using namespace constraint;
using namespace algorithm;

using namespace toppra::constraint;
using namespace toppra::algorithm;
using namespace toppra::parametrizer;

class TrajectoryInterpolator {
    public:
        TrajectoryInterpolator(double dt = 0.01)
            : dt_(dt), dof_(0), solve_time_(0.0) {}
    
        // 设置关节速度和加速度约束
        void setMotionConstraints(int dof, double vlim = 1.0, double alim = 2.0) {
            dof_ = dof;
            // 构造上下界 vectors
            VectorXd lowerV = VectorXd::Constant(dof, -vlim);
            VectorXd upperV = VectorXd::Constant(dof,  vlim);
            VectorXd lowerA = VectorXd::Constant(dof, -alim);
            VectorXd upperA = VectorXd::Constant(dof,  alim);

            constraints_.clear();
            constraints_.push_back(std::make_shared<LinearJointVelocity>(lowerV, upperV));
            constraints_.push_back(std::make_shared<LinearJointAcceleration>(lowerA, upperA));
        }
    
        // 计算轨迹
        void compute(const MatrixXd& waypoints) {
            if (constraints_.empty()) {
                throw std::runtime_error("Please set motion constraints first.");
            }
            waypoints_ = waypoints;
            // 2) 把 waypoints 转成 PiecewisePolyPath 要的 Vectors
            //    Vectors 就是 std::vector<Eigen::VectorXd>
            toppra::Vectors positions;
            for (int i = 0; i < waypoints.rows(); ++i)
                positions.push_back(waypoints.row(i).transpose());

            toppra::Vector times(positions.size());
            times.setLinSpaced(0, 1);
            
            std::array<toppra::BoundaryCond, 2> boundary_cond;
            toppra::BoundaryCond bc{"clamped"};
            boundary_cond[0] = bc;
            boundary_cond[1] = bc;
            // 4) 静态函数生成一条 3 次样条 (CubicSpline)
            auto path = PiecewisePolyPath::CubicSpline(positions,times, boundary_cond);
            
            path_ = std::make_shared<toppra::PiecewisePolyPath>(path);

            // 构建 TOPP-RA 求解器
            algorithm::TOPPRA algo(constraints_, path_);
            algo.solver(std::make_shared<solver::Seidel>());
            ReturnCode ret_code = algo.computePathParametrization(0 , 0 );// /*Zero Initial Velocity*/, /*Zero Final Velocity*/
            
            data_ = algo.getParameterizationData();
            trajectory_ = std::make_shared<parametrizer::ConstAccel>(path_, data_.gridpoints, data_.parametrization);


        }
    
        // 采样轨迹，返回时间戳、位置、速度、加速度
        void sample(std::vector<double>& ts,
                    MatrixXd& pos,
                    MatrixXd& vel,
                    MatrixXd& acc) const {
            if (!trajectory_) {
                throw std::runtime_error("Trajectory not yet computed.");
            }
            
                        
            auto path_interval = trajectory_->pathInterval();
            double duration = path_interval[1] - path_interval[0];
            std::cout << "Traj duration:" << duration << std::endl;
            int rows = std::ceil(duration / dt_);
            toppra::Vector time(rows);
            time.setLinSpaced(0, duration);
            std::cout << "Time size:" << time.size() << std::endl;
            //插值后的位置、速度、加速度
            toppra::Vectors topp_pos_vec_ = trajectory_->eval(time, 0);
            toppra::Vectors topp_vel_vec_ = trajectory_->eval(time, 1);
            toppra::Vectors topp_acc_vec_ = trajectory_->eval(time, 2);
            int N = time.size();  // 从 i=0 到 i=rows
            // 4) 分配输出矩阵 [N x dof_]
            ts.resize(N);
            pos.resize(N, dof_);
            vel.resize(N, dof_);
            acc.resize(N, dof_);
            VectorXd u(1);
            for (size_t i = 0; i < time.size(); i++)
            {
                ts[i] = time[i];
                u(0) = time[i];
                pos.row(i) = trajectory_->eval(u, 0)[0].transpose();
                vel.row(i) = trajectory_->eval(u, 1)[0].transpose();
                acc.row(i) = trajectory_->eval(u, 2)[0].transpose();
            }
        }
    
        // 获取单点状态
        VectorXd getPosition(double t) const { 
            VectorXd u(1); 
            u(0) = t; 
            return trajectory_->eval(u, 0)[0]; 
        }
        VectorXd getVelocity(double t) const { 
            VectorXd u(1); 
            u(0) = t; 
            return trajectory_->eval(u, 1)[0]; 
        }
        VectorXd getAcceleration(double t) const { 
            VectorXd u(1); 
            u(0) = t; 
            return trajectory_->eval(u, 2)[0]; 
        }
    
    private:
        double dt_;
        int dof_;
        double solve_time_;         //
        MatrixXd waypoints_;        //
        LinearConstraintPtrs constraints_;
        GeometricPathPtr path_;
        ParametrizationData data_;
        std::shared_ptr<parametrizer::ConstAccel> trajectory_;
    };
    
int main() {

    MatrixXd waypoints(4, 4);
    waypoints << 0.0, 0.0, 0.0, 0.0,
                    1.0, 0.5, 0.3, -0.2,
                    1.5, -0.2, 0.6, 0.2,
                    2.0, 0.0, 0.0, 0.0;
    
    // 创建轨迹插值器实例
    TrajectoryInterpolator interpolator(0.01);

    // 设置约束 (4个自由度, vlim=1.0, alim=2.0)
    interpolator.setMotionConstraints(waypoints.cols(), 1.0, 2.0);

    // 计算轨迹
    interpolator.compute(waypoints);

    // 采样并打印结果
    std::vector<double> ts;
    MatrixXd pos, vel, acc;
    interpolator.sample(ts, pos, vel, acc);
    
    // 设置输出精度
    std::cout << std::fixed << std::setprecision(4);
    
    // 打印采样结果
    for (size_t i = 0; i < ts.size(); ++i) {
        if (ts[i] > 3.0)  // 只打印 t > 3.4 的点
        {
             std::cout << "t=" << ts[i] << ", pos=[" << pos.row(i) << "], vel=[" << vel.row(i) << "], acc=[" << acc.row(i) << "]\n";
        }
    }
    return 0;
}

// const std::string urdf_filename =  "/robot.urdf";
// pinocchio::Model model;
// pinocchio::urdf::buildModel(urdf_filename, model);
// pinocchio::Data data(model);

// auto jntTorqueCnt_ptr = std::make_shared<toppra::constraint::jointTorque::Pinocchio<>>(model);

// toppra::LinearConstraintPtrs constraints = toppra::LinearConstraintPtrs{
//     std::make_shared<toppra::constraint::LinearJointVelocity>(velLimitLower, velLimitUpper),
//     std::make_shared<toppra::constraint::LinearJointAcceleration>(accLimitLower, accLimitUpper),
//     jntTorqueCnt_ptr};//velLimitLower，accLimitLower均为Eigen::VectorXd
