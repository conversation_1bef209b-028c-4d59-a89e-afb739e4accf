#include <iostream>
#include <vector>
#include <iomanip> // For std::setprecision

#include "../include/trajectory/ToppraInterpolator.hpp"

constexpr int DOF = 3;

int main() {
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;

    // Create ToppraInterpolator with new interface
    ToppraInterpolator<DOF> interpolator(0.01, "");

    // Set motion constraints using new MotionConstraints structure
    MotionConstraints<DOF> constraints;
    constraints.max_velocity = VectorDOF::Constant(1.0);
    constraints.max_acceleration = VectorDOF::Constant(2.0);
    constraints.max_jerk = VectorDOF::Constant(5.0);
    interpolator.setConstraints(constraints);

    // Create waypoints using new MotionState structure
    std::vector<MotionState<DOF>> waypoints;

    // Start waypoint
    MotionState<DOF> start_state;
    start_state.position << 0.0, 0.0, 0.0;
    start_state.velocity << 0.0, 0.0, 0.0;
    start_state.acceleration << 0.0, 0.0, 0.0;
    waypoints.push_back(start_state);

    // Intermediate waypoint
    MotionState<DOF> mid_state;
    mid_state.position << 1.0, 0.5, -0.2;
    mid_state.velocity << 0.0, 0.0, 0.0;
    mid_state.acceleration << 0.0, 0.0, 0.0;
    waypoints.push_back(mid_state);

    // End waypoint
    MotionState<DOF> end_state;
    end_state.position << 2.0, 0.0, 0.0;
    end_state.velocity << 0.0, 0.0, 0.0;
    end_state.acceleration << 0.0, 0.0, 0.0;
    waypoints.push_back(end_state);

    // Compute trajectory using new interface (offline planning)
    bool success = interpolator.computeOffline(waypoints);

    if (!success) {
        std::cerr << "Failed to compute trajectory: " << interpolator.getLastError() << std::endl;
        return -1;
    }

    std::cout << "Trajectory computation successful!" << std::endl;
    std::cout << "Interpolator type: " << interpolator.getTypeName() << std::endl;
    std::cout << "Trajectory duration: " << interpolator.getDuration() << " seconds" << std::endl;


    // Get trajectory buffer and sample at regular intervals
    const auto& trajectory_buffer = interpolator.getTrajectoryBuffer();
    double dt = 0.02;
    double duration = interpolator.getDuration();
    int num_samples = static_cast<int>(duration / dt) + 1;

    // Set output precision
    std::cout << std::fixed << std::setprecision(4);
    std::cout << "\nTrajectory samples using getTrajectoryBuffer():" << std::endl;
    std::cout << "Buffer size: " << trajectory_buffer.size() << " states" << std::endl;

    // Sample trajectory using the TrajectoryBuffer (show first 10 and last 10)
    for (int i = 0; i < num_samples; ++i) {
        // Only print first 10 and last 10 samples to avoid too much output
        if (i < 10 || i >= num_samples - 10) {
            double t = std::min(i * dt, duration);
            TrajectoryState<DOF> state = trajectory_buffer.getStateAtTime(t);

            if (state.valid) {
                std::cout << "t=" << t
                          << ", pos=[" << state.position.transpose() << "]"
                          << ", vel=[" << state.velocity.transpose() << "]"
                          << ", acc=[" << state.acceleration.transpose() << "]" << std::endl;
            }
        } else if (i == 10) {
            std::cout << "... (skipping intermediate samples) ..." << std::endl;
        }
    }

    return 0;
}