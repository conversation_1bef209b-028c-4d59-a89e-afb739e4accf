#include <iostream>
#include <vector>
#include <iomanip> // For std::setprecision

#include "../include/trajectory/RuckigInterpolator.hpp"

constexpr int DOF = 3;

int main() {
    using VectorDOF = Eigen::Matrix<double, DOF, 1>;

    // Create RuckigInterpolator with new interface
    RuckigInterpolator<DOF> interpolator(0.01, "");

    // Set motion constraints using new MotionConstraints structure
    MotionConstraints<DOF> constraints;
    constraints.max_velocity = VectorDOF::Constant(1.0);
    constraints.max_acceleration = VectorDOF::Constant(2.0);
    constraints.max_jerk = VectorDOF::Constant(5.0);
    interpolator.setConstraints(constraints);

    // Create motion states using new MotionState structure
    MotionState<DOF> start_state;
    start_state.position << 0.0, 0.0, 0.0;
    start_state.velocity << 0.0, 0.0, 0.0;
    start_state.acceleration << 0.0, 0.0, 0.0;

    MotionState<DOF> target_state;
    target_state.position << 1.0, -0.5, 0.8;
    target_state.velocity << 0.0, 0.0, 0.0;
    target_state.acceleration << 0.0, 0.0, 0.0;

    // Compute trajectory using new interface
    bool success = interpolator.computeOnline(start_state, target_state);

    if (!success) {
        std::cerr << "Failed to compute trajectory: " << interpolator.getLastError() << std::endl;
        return -1;
    }

    std::cout << "Trajectory computation successful!" << std::endl;
    std::cout << "Interpolator type: " << interpolator.getTypeName() << std::endl;
    std::cout << "Trajectory duration: " << interpolator.getDuration() << " seconds" << std::endl;

    // Sample and print trajectory using TrajectoryBuffer's built-in method
    std::cout << "\n";
    const auto& trajectory_buffer = interpolator.getTrajectoryBuffer();

    // Debug information
    std::cout << "Debug: Buffer size = " << trajectory_buffer.size() << std::endl;
    std::cout << "Debug: Buffer empty = " << trajectory_buffer.isEmpty() << std::endl;
    std::cout << "Debug: Duration = " << trajectory_buffer.getDuration() << std::endl;

    trajectory_buffer.sampleAndPrint(0.01, 50, 4);  // dt=0.01s, max 50 samples, 4 decimal places

    return 0;
}