cmake_minimum_required(VERSION 3.10)
project(ruckig_examples)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(ruckig REQUIRED CONFIG PATHS ${CMAKE_SOURCE_DIR}/../third_party/ruckig/build)

# Build all .cpp examples in this folder
file(GLOB EXAMPLE_SRCS CONFIGURE_DEPENDS "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
foreach(src ${EXAMPLE_SRCS})
    get_filename_component(name ${src} NAME_WE)
    add_executable(${name} ${src})
    target_link_libraries(${name} PRIVATE ruckig::ruckig)
endforeach()