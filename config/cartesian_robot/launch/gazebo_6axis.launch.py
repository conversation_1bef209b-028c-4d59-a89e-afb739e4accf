#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node


def generate_launch_description():
    # Get the package directory
    pkg_share = get_package_share_directory('cartesian_robot')
    gazebo_ros_share = get_package_share_directory('gazebo_ros')

    # Path to the 6-axis URDF file
    urdf_file = os.path.join(pkg_share, 'urdf', 'cartesian_6axis.urdf')
    
    # Include Gazebo launch file
    gazebo_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(gazebo_ros_share, 'launch', 'gazebo.launch.py')
        ])
    )
    
    # Static transform publisher for base_footprint
    static_tf_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='tf_footprint_base',
        arguments=['0', '0', '0', '0', '0', '0', 'base_link', 'base_footprint'],
        output='screen'
    )
    
    # Spawn robot in Gazebo
    spawn_entity_node = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        name='spawn_model',
        arguments=['-file', urdf_file, '-entity', 'cartesian_6axis'],
        output='screen'
    )
    
    # Fake joint calibration publisher
    calibration_publisher_node = Node(
        package='ros2topic',
        executable='pub',
        name='fake_joint_calibration',
        arguments=['/calibrated', 'std_msgs/msg/Bool', 'data: true'],
        output='screen'
    )
    
    return LaunchDescription([
        gazebo_launch,
        static_tf_node,
        spawn_entity_node,
        calibration_publisher_node
    ])
