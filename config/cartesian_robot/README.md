# 3axis_robot - ROS2 URDF Package

这是一个已转换为ROS2格式的机器人URDF描述包，包含3轴和6轴机器人模型。

## 包结构

```
3axis_robot/
├── CMakeLists.txt          # ROS2 ament_cmake构建文件
├── package.xml             # ROS2 package.xml (format 3)
├── config/                 # 配置文件
│   ├── joint_names_3axis_robot.yaml
│   └── joint_names_6axis_robot.yaml
├── launch/                 # Launch文件
│   ├── display.launch.py   # ROS2 3轴机器人display launch文件
│   ├── gazebo.launch.py    # ROS2 3轴机器人gazebo launch文件
│   ├── display_6axis.launch.py  # ROS2 6轴机器人display launch文件
│   ├── gazebo_6axis.launch.py   # ROS2 6轴机器人gazebo launch文件
│   ├── display.launch      # 原ROS1 launch文件（保留）
│   └── gazebo.launch       # 原ROS1 launch文件（保留）
├── meshes/                 # 3D模型文件
│   ├── baselink.STL
│   ├── link1.STL
│   ├── link2.STL
│   ├── link3.STL
│   └── tool.STL
├── urdf/                   # URDF文件
│   ├── 3axis_robot.urdf    # 原3轴机器人URDF
│   ├── 6axis_robot.urdf    # 6轴机器人URDF
│   └── 3axis_robot.csv
└── textures/               # 纹理文件
```

## 主要变更

### 1. CMakeLists.txt
- 从catkin构建系统转换为ament_cmake
- 更新CMake最低版本要求为3.8
- 添加了编译选项和测试支持

### 2. package.xml
- 从format 2升级到format 3
- 更新依赖项为ROS2对应的包：
  - `rviz` → `rviz2`
  - `gazebo` → `gazebo_ros`
  - 添加了`xacro`依赖
- 使用`ament_cmake`作为构建工具

### 3. Launch文件
- 创建了ROS2格式的Python launch文件：
  - `display.launch.py` - 用于在RViz2中显示机器人
  - `gazebo.launch.py` - 用于在Gazebo中仿真机器人
- 保留了原始的ROS1 launch文件作为参考

## 依赖项

确保安装了以下ROS2包：
- `robot_state_publisher`
- `rviz2`
- `joint_state_publisher_gui`
- `gazebo_ros`
- `xacro`
- `tf2_ros`

## 构建和使用

### 构建包
```bash
# 在ROS2工作空间中
colcon build --packages-select 3axis_robot
source install/setup.bash
```

### 运行3轴机器人显示
```bash
ros2 launch 3axis_robot display.launch.py
```

### 运行6轴机器人显示
```bash
ros2 launch 3axis_robot display_6axis.launch.py
```

### 运行3轴机器人Gazebo仿真
```bash
ros2 launch 3axis_robot gazebo.launch.py
```

### 运行6轴机器人Gazebo仿真
```bash
ros2 launch 3axis_robot gazebo_6axis.launch.py
```

## 机器人描述

### 3轴笛卡尔机器人 (cartesian_3axis.urdf)
- 1个基座链接 (baselink)
- 3个移动链接 (link1, link2, link3)
- 1个工具链接 (tool)
- 3个prismatic关节 (joint1, joint2, joint3)
- 1个固定关节 (tool_joint)

关节限制：
- joint1: 0 到 0.5 米 (X轴平移)
- joint2: 0 到 0.25 米 (Y轴平移)
- joint3: 0 到 0.07 米 (Z轴平移)

### 6轴笛卡尔机器人 (cartesian_6axis.urdf)
- 1个基座链接 (baselink)
- 6个链接 (link1, link2, link3, link4, link5, link6)
- 1个末端执行器 (end_effector)
- 前3个prismatic关节 (joint1, joint2, joint3) - 平移轴
- 后3个revolute关节 (joint4, joint5, joint6) - 旋转轴
- 1个固定关节 (end_effector_joint)

关节限制：
- joint1: 0 到 0.5 米 (X轴平移)
- joint2: 0 到 0.25 米 (Y轴平移)
- joint3: 0 到 0.07 米 (Z轴平移)
- joint4: -π 到 π 弧度 (X轴旋转)
- joint5: -π 到 π 弧度 (Y轴旋转)
- joint6: -π 到 π 弧度 (Z轴旋转)

## 注意事项

1. 确保ROS2环境已正确设置
2. 如果使用Gazebo，确保已安装gazebo_ros包
3. URDF文件与ROS2完全兼容，无需修改
4. 原始的ROS1 launch文件已保留，可以作为参考或在混合环境中使用
