#!/bin/bash

# robot_infra 库打包发布脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 版本信息
VERSION="1.0.0"
PACKAGE_NAME="robot_infra"
RELEASE_DIR="release"

print_info "robot_infra 库打包脚本 v${VERSION}"

# 清理并创建发布目录
if [[ -d "$RELEASE_DIR" ]]; then
    print_info "清理现有发布目录..."
    rm -rf "$RELEASE_DIR"
fi

mkdir -p "$RELEASE_DIR"
cd "$RELEASE_DIR"

print_info "创建发布包结构..."

# 创建目录结构
mkdir -p "${PACKAGE_NAME}-${VERSION}/"{lib,include,examples,docs}

# 构建库
print_info "构建动态库..."
cd ..
if [[ ! -d "build" ]]; then
    mkdir build
fi

cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)

# 复制库文件
print_info "复制库文件..."
cp librobot_infra.so.${VERSION} "../${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/lib/"
cd "../${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/lib"
ln -s librobot_infra.so.${VERSION} librobot_infra.so.1
ln -s librobot_infra.so.1 librobot_infra.so

# 复制头文件
print_info "复制头文件..."
cd ../../..
cp -r include/* "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/include/"

# 复制示例
print_info "复制示例文件..."
cp -r examples/use_installed_lib "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/examples/"

# 复制文档
print_info "复制文档..."
cp INSTALL.md "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/docs/"
cp README.md "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/docs/" 2>/dev/null || echo "README.md not found, skipping"

# 创建安装脚本
print_info "创建安装脚本..."
cat > "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/install.sh" << 'EOF'
#!/bin/bash

# robot_infra 库安装脚本

set -e

PREFIX="/usr/local"
if [[ $# -gt 0 ]]; then
    PREFIX="$1"
fi

echo "安装 robot_infra 库到 $PREFIX"

# 安装库文件
echo "安装库文件..."
sudo cp lib/librobot_infra.so.* "$PREFIX/lib/"
sudo ln -sf "$PREFIX/lib/librobot_infra.so.1.0.0" "$PREFIX/lib/librobot_infra.so.1"
sudo ln -sf "$PREFIX/lib/librobot_infra.so.1" "$PREFIX/lib/librobot_infra.so"

# 安装头文件
echo "安装头文件..."
sudo mkdir -p "$PREFIX/include/robot_infra"
sudo cp -r include/* "$PREFIX/include/robot_infra/"

# 更新动态库缓存
echo "更新动态库缓存..."
sudo ldconfig

echo "安装完成!"
echo "库文件: $PREFIX/lib/librobot_infra.so"
echo "头文件: $PREFIX/include/robot_infra/"

# 验证安装
if [[ -f "$PREFIX/lib/librobot_infra.so" ]]; then
    echo "✓ 库文件安装成功"
else
    echo "✗ 库文件安装失败"
fi

if [[ -d "$PREFIX/include/robot_infra" ]]; then
    echo "✓ 头文件安装成功"
else
    echo "✗ 头文件安装失败"
fi
EOF

chmod +x "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/install.sh"

# 创建卸载脚本
print_info "创建卸载脚本..."
cat > "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/uninstall.sh" << 'EOF'
#!/bin/bash

# robot_infra 库卸载脚本

PREFIX="/usr/local"
if [[ $# -gt 0 ]]; then
    PREFIX="$1"
fi

echo "从 $PREFIX 卸载 robot_infra 库"

# 删除库文件
echo "删除库文件..."
sudo rm -f "$PREFIX/lib/librobot_infra.so"*

# 删除头文件
echo "删除头文件..."
sudo rm -rf "$PREFIX/include/robot_infra"

# 更新动态库缓存
echo "更新动态库缓存..."
sudo ldconfig

echo "卸载完成!"
EOF

chmod +x "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/uninstall.sh"

# 创建README
print_info "创建发布README..."
cat > "${RELEASE_DIR}/${PACKAGE_NAME}-${VERSION}/README.txt" << EOF
robot_infra v${VERSION} 发布包
=============================

这是 robot_infra 工业机器人基础设施库的预编译发布包。

包含内容:
- lib/          动态库文件 (librobot_infra.so.${VERSION})
- include/      头文件
- examples/     使用示例
- docs/         文档
- install.sh    安装脚本
- uninstall.sh  卸载脚本

快速安装:
1. 解压此包
2. 运行: sudo ./install.sh
3. 可选择安装路径: sudo ./install.sh /opt/robot_infra

依赖项:
- libeigen3-dev
- libzmq3-dev

在 Ubuntu/Debian 上安装依赖:
sudo apt install libeigen3-dev libzmq3-dev

使用方法:
请参考 docs/INSTALL.md 获取详细使用说明。

编译信息:
- 版本: ${VERSION}
- 构建类型: Release
- C++ 标准: C++17
- 构建时间: $(date)
EOF

# 创建压缩包
print_info "创建压缩包..."
cd "${RELEASE_DIR}"
tar -czf "${PACKAGE_NAME}-${VERSION}-linux-x86_64.tar.gz" "${PACKAGE_NAME}-${VERSION}/"

# 创建校验和
print_info "生成校验和..."
sha256sum "${PACKAGE_NAME}-${VERSION}-linux-x86_64.tar.gz" > "${PACKAGE_NAME}-${VERSION}-linux-x86_64.tar.gz.sha256"

print_success "打包完成!"
print_info "发布文件:"
ls -la "${PACKAGE_NAME}-${VERSION}-linux-x86_64.tar.gz"*

print_info "包内容:"
tar -tzf "${PACKAGE_NAME}-${VERSION}-linux-x86_64.tar.gz" | head -20

print_success "发布包已创建在 ${RELEASE_DIR}/ 目录中"
print_info "可以使用以下命令测试安装:"
echo "  tar -xzf ${PACKAGE_NAME}-${VERSION}-linux-x86_64.tar.gz"
echo "  cd ${PACKAGE_NAME}-${VERSION}"
echo "  sudo ./install.sh"
