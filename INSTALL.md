# robot_infra 库安装和使用指南

## 概述

robot_infra 是一个工业机器人基础设施库，提供了机器人通信、轨迹规划和控制功能。

## 依赖项

### 必需依赖
- CMake >= 3.16
- C++17 编译器
- Eigen3
- libzmq3-dev

### 可选依赖
- ruckig (轨迹规划)
- toppra (轨迹优化)

### Ubuntu/Debian 安装依赖
```bash
sudo apt update
sudo apt install cmake build-essential libeigen3-dev libzmq3-dev pkg-config
```

## 构建和安装

### 方法1: 使用自动化脚本 (推荐)

```bash
# 给脚本执行权限
chmod +x build_and_install.sh

# 默认安装 (Release版本，安装到/usr/local)
./build_and_install.sh

# 自定义安装
./build_and_install.sh --debug --prefix /opt/robot_infra --package

# 查看所有选项
./build_and_install.sh --help
```

### 方法2: 手动构建

```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/usr/local

# 构建
make -j$(nproc)

# 运行测试
make test

# 安装
sudo make install

# 更新动态库缓存
sudo ldconfig
```

## 安装后的文件结构

```
/usr/local/
├── lib/
│   ├── librobot_infra.so.1.0.0    # 动态库
│   ├── librobot_infra.so.1         # 符号链接
│   ├── librobot_infra.so           # 符号链接
│   ├── cmake/robot_infra/          # CMake配置文件
│   └── pkgconfig/robot_infra.pc    # pkg-config文件
└── include/robot_infra/            # 头文件
    ├── industRob.h
    ├── comm/
    └── trajectory/
```

## 使用方法

### 方法1: CMake (推荐)

在你的 `CMakeLists.txt` 中：

```cmake
cmake_minimum_required(VERSION 3.16)
project(my_robot_app)

set(CMAKE_CXX_STANDARD 17)

# 查找robot_infra库
find_package(robot_infra REQUIRED)

# 创建可执行文件
add_executable(my_app main.cpp)

# 链接库
target_link_libraries(my_app robot_infra::robot_infra)
```

### 方法2: pkg-config

```bash
# 编译单个文件
g++ -std=c++17 main.cpp $(pkg-config --cflags --libs robot_infra) -o my_app

# 在Makefile中使用
CFLAGS += $(shell pkg-config --cflags robot_infra)
LIBS += $(shell pkg-config --libs robot_infra)
```

### 方法3: 直接链接

```bash
g++ -std=c++17 -I/usr/local/include/robot_infra main.cpp -L/usr/local/lib -lrobot_infra -o my_app
```

## 代码示例

```cpp
#include <robot_infra/industRob.h>
#include <iostream>

int main() {
    try {
        // 创建机器人实例
        industRob robot(CommType::ZMQ);
        
        // 连接机器人
        robot.connect();
        
        if (robot.isConnected()) {
            std::cout << "机器人连接成功!" << std::endl;
            
            // 初始化机器人
            robot.init();
            
            // 获取当前关节位置
            auto joints = robot.getJoint();
            
            // 执行运动
            std::vector<double> target_pose = {0.5, 0.0, 0.3, 0.0, 0.0, 0.0};
            robot.movL(target_pose, 0.5, 0.2, 0.01);
        }
        
        robot.disconnect();
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
```

## 验证安装

运行以下命令验证安装是否成功：

```bash
# 检查库文件
ls -la /usr/local/lib/librobot_infra*

# 检查头文件
ls -la /usr/local/include/robot_infra/

# 检查pkg-config
pkg-config --modversion robot_infra
pkg-config --cflags robot_infra
pkg-config --libs robot_infra

# 检查CMake配置
find /usr/local -name "robot_infraConfig.cmake"
```

## 卸载

```bash
# 如果使用默认安装路径
sudo rm -rf /usr/local/include/robot_infra
sudo rm -f /usr/local/lib/librobot_infra*
sudo rm -rf /usr/local/lib/cmake/robot_infra
sudo rm -f /usr/local/lib/pkgconfig/robot_infra.pc
sudo ldconfig
```

## 创建安装包

```bash
# 在build目录中
make package

# 这将创建以下包文件:
# robot_infra-1.0.0-Linux.tar.gz  (TGZ包)
# robot_infra-1.0.0-Linux.deb     (DEB包，如果在Debian/Ubuntu上)
```

## 故障排除

### 1. 找不到依赖项
确保安装了所有必需的依赖项，特别是 Eigen3 和 libzmq。

### 2. CMake找不到库
确保 `CMAKE_PREFIX_PATH` 包含安装路径：
```bash
export CMAKE_PREFIX_PATH=/usr/local:$CMAKE_PREFIX_PATH
```

### 3. 运行时找不到动态库
确保库路径在 `LD_LIBRARY_PATH` 中或运行了 `ldconfig`：
```bash
export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
# 或
sudo ldconfig
```

### 4. 权限问题
如果安装到系统目录，确保使用 `sudo`。

## 支持

如有问题，请检查：
1. 依赖项是否正确安装
2. 编译器是否支持C++17
3. CMake版本是否 >= 3.16
